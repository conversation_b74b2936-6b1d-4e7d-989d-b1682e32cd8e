================================================================================
SCANNING OPERATION LOG - 2025-07-09 16:49:49
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752050986\scan_1752050986_flake_data.csv
Grid Steps: 0 x 0
Edge Method: general
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752050986
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752050986\debug_screenshots
================================================================================

[2025-07-09 16:49:49.942] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-09 16:49:49.951] [INFO] [SYSTEM] Using custom scan folder: scan_1752050986
[2025-07-09 16:49:49.978] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-09 16:49:50.111] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-09 16:49:50.129] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-09 16:49:50.139] [INFO] [STATUS] ✗ SCAN ABORTED: Unexpected error in corner detection: 'CentralizedCameraManager' object has no attribute 'get_image'
[2025-07-09 16:49:50.148] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1752050986\scan_1752050986_flake_data.csv
[2025-07-09 16:49:50.158] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-09 16:49:50.171] [INFO] [STATUS] Scanning for annotated images...
[2025-07-09 16:49:50.185] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
