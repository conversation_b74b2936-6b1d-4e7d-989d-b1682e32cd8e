================================================================================
SCANNING OPERATION LOG - 2025-07-09 18:35:20
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\20\20_flake_data.csv
Grid Steps: 0 x 0
Edge Method: background
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\20
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\20\debug_screenshots
================================================================================

[2025-07-09 18:35:20.609] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-09 18:35:20.619] [INFO] [SYSTEM] Using custom scan folder: 20
[2025-07-09 18:35:20.636] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-09 18:35:20.747] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-09 18:35:20.756] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-09 18:35:20.806] [ERROR] [ERROR] ✗ SCAN ABORTED: Camera capture failed: Failed to send command. Error code: -10
[2025-07-09 18:35:20.816] [INFO] [STATUS] ✗ Cannot proceed without a valid hybrid corner reference
[2025-07-09 18:35:20.828] [INFO] [STATUS] ✗ Please check:
[2025-07-09 18:35:20.838] [INFO] [STATUS]   → Chip positioning and visibility
[2025-07-09 18:35:20.849] [INFO] [STATUS]   → Edge detection parameters
[2025-07-09 18:35:20.858] [INFO] [STATUS]   → Image quality and lighting
[2025-07-09 18:35:20.868] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\20\20_flake_data.csv
[2025-07-09 18:35:20.878] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-09 18:35:20.887] [INFO] [STATUS] Scanning for annotated images...
[2025-07-09 18:35:20.896] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
