#!/usr/bin/env python3
"""
GUI Components for Hybrid Corner Feature Hashing System

This module provides PyQt6 GUI components for visualizing and debugging
the hybrid corner alignment system, following established patterns from
the existing codebase.

Author: Augment Agent
Date: 2025-07-02
"""

import sys
import os
import json
import numpy as np
from typing import List, Dict, Optional, Tuple
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTextEdit, QGroupBox, QGridLayout, QProgressBar, QFileDialog,
    QMessageBox, QTableWidget, QTableWidgetItem, QHeaderView,
    QSplitter, QFrame, QScrollArea
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QPainter, QPen, QColor

import cv2
from hybrid_corner_alignment import (
    HybridCornerAlignmentSystem, HybridFeature, 
    save_hybrid_reference, load_hybrid_reference
)
from scanning import StageController


class HybridFeatureVisualizationWidget(QWidget):
    """
    Widget for visualizing detected hybrid features (flakes + edge keypoints).
    
    This widget displays the corner image with overlaid feature annotations,
    following the established pattern from edge detection debug panels.
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.corner_image = None
        self.features = []
        
    def setup_ui(self):
        """Setup the user interface."""
        layout = QVBoxLayout(self)
        
        # Title
        title = QLabel("Hybrid Feature Visualization")
        title.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Image display area
        self.image_label = QLabel()
        self.image_label.setMinimumSize(400, 300)
        self.image_label.setStyleSheet("border: 1px solid gray;")
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setText("No image loaded")
        
        # Scroll area for image
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.image_label)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        # Feature statistics
        stats_group = QGroupBox("Feature Statistics")
        stats_layout = QGridLayout(stats_group)
        
        self.total_features_label = QLabel("Total: 0")
        self.flake_count_label = QLabel("Flakes: 0")
        self.keypoint_count_label = QLabel("Edge Keypoints: 0")
        
        stats_layout.addWidget(QLabel("Features:"), 0, 0)
        stats_layout.addWidget(self.total_features_label, 0, 1)
        stats_layout.addWidget(self.flake_count_label, 1, 0)
        stats_layout.addWidget(self.keypoint_count_label, 1, 1)
        
        layout.addWidget(stats_group)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.save_image_btn = QPushButton("Save Annotated Image")
        self.save_image_btn.clicked.connect(self.save_annotated_image)
        self.save_image_btn.setEnabled(False)
        
        self.clear_btn = QPushButton("Clear")
        self.clear_btn.clicked.connect(self.clear_display)
        
        button_layout.addWidget(self.save_image_btn)
        button_layout.addWidget(self.clear_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
    
    def update_display(self, corner_image: np.ndarray, features: List[HybridFeature]):
        """
        Update the display with new corner image and features.
        
        Args:
            corner_image: Corner region image (BGR format)
            features: List of detected hybrid features
        """
        self.corner_image = corner_image.copy()
        self.features = features.copy()
        
        # Create annotated image
        annotated = self._create_annotated_image()
        
        # Convert to QPixmap and display
        height, width, channel = annotated.shape
        bytes_per_line = 3 * width
        q_image = QPixmap.fromImage(
            QPixmap.fromImage(annotated.data, width, height, bytes_per_line, QPixmap.Format.Format_RGB888)
        )
        
        self.image_label.setPixmap(q_image)
        self.image_label.resize(q_image.size())
        
        # Update statistics
        self._update_statistics()
        
        # Enable save button
        self.save_image_btn.setEnabled(True)
    
    def _create_annotated_image(self) -> np.ndarray:
        """Create annotated image with feature overlays."""
        if self.corner_image is None:
            return np.zeros((300, 400, 3), dtype=np.uint8)
        
        # Convert BGR to RGB for Qt display
        annotated = cv2.cvtColor(self.corner_image, cv2.COLOR_BGR2RGB)
        
        # Draw features
        for feature in self.features:
            x, y = int(feature.pixel_x), int(feature.pixel_y)
            
            if feature.feature_type == "flake":
                # Draw flakes as green circles
                cv2.circle(annotated, (x, y), 8, (0, 255, 0), 2)
                cv2.putText(annotated, f"F", (x+12, y+5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            else:
                # Draw edge keypoints as blue circles
                cv2.circle(annotated, (x, y), 5, (0, 0, 255), 2)
                cv2.putText(annotated, f"E", (x+8, y+5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 2)
        
        return annotated
    
    def _update_statistics(self):
        """Update feature statistics display."""
        total = len(self.features)
        flakes = sum(1 for f in self.features if f.feature_type == "flake")
        keypoints = sum(1 for f in self.features if f.feature_type == "edge_keypoint")
        
        self.total_features_label.setText(f"Total: {total}")
        self.flake_count_label.setText(f"Flakes: {flakes}")
        self.keypoint_count_label.setText(f"Edge Keypoints: {keypoints}")
    
    def save_annotated_image(self):
        """Save the annotated image to file."""
        if self.corner_image is None:
            return
        
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save Annotated Image", "", "PNG Files (*.png);;All Files (*)"
        )
        
        if filename:
            annotated = self._create_annotated_image()
            # Convert back to BGR for OpenCV saving
            bgr_image = cv2.cvtColor(annotated, cv2.COLOR_RGB2BGR)
            cv2.imwrite(filename, bgr_image)
            QMessageBox.information(self, "Success", f"Image saved to {filename}")
    
    def clear_display(self):
        """Clear the display."""
        self.corner_image = None
        self.features = []
        self.image_label.clear()
        self.image_label.setText("No image loaded")
        self._update_statistics()
        self.save_image_btn.setEnabled(False)


class TransformationMatrixWidget(QWidget):
    """
    Widget for displaying transformation matrix and alignment results.
    
    This widget shows the calculated transformation matrix, alignment statistics,
    and provides controls for applying transformations.
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.transformation_matrix = None
        
    def setup_ui(self):
        """Setup the user interface."""
        layout = QVBoxLayout(self)
        
        # Title
        title = QLabel("Transformation Matrix")
        title.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Matrix display
        matrix_group = QGroupBox("Affine Transformation Matrix")
        matrix_layout = QVBoxLayout(matrix_group)
        
        self.matrix_text = QTextEdit()
        self.matrix_text.setMaximumHeight(100)
        self.matrix_text.setFont(QFont("Courier", 10))
        self.matrix_text.setReadOnly(True)
        matrix_layout.addWidget(self.matrix_text)
        
        layout.addWidget(matrix_group)
        
        # Alignment statistics
        stats_group = QGroupBox("Alignment Statistics")
        stats_layout = QGridLayout(stats_group)
        
        self.feature_matches_label = QLabel("Feature Matches: 0")
        self.match_quality_label = QLabel("Match Quality: 0.000")
        self.corner_offset_label = QLabel("Corner Offset: (0.0, 0.0) μm")
        
        stats_layout.addWidget(self.feature_matches_label, 0, 0)
        stats_layout.addWidget(self.match_quality_label, 0, 1)
        stats_layout.addWidget(self.corner_offset_label, 1, 0, 1, 2)
        
        layout.addWidget(stats_group)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.export_matrix_btn = QPushButton("Export Matrix")
        self.export_matrix_btn.clicked.connect(self.export_matrix)
        self.export_matrix_btn.setEnabled(False)
        
        self.clear_btn = QPushButton("Clear")
        self.clear_btn.clicked.connect(self.clear_display)
        
        button_layout.addWidget(self.export_matrix_btn)
        button_layout.addWidget(self.clear_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # Add stretch to push everything to top
        layout.addStretch()
    
    def update_transformation(self, result: Dict):
        """
        Update the display with transformation results.
        
        Args:
            result: Transformation result dictionary from hybrid alignment
        """
        if not result.get('success', False):
            self.clear_display()
            return
        
        # Store transformation matrix
        self.transformation_matrix = np.array(result['transformation_matrix'])
        
        # Display matrix
        matrix_str = "Transformation Matrix (2x3):\n"
        matrix_str += f"[{self.transformation_matrix[0,0]:8.4f} {self.transformation_matrix[0,1]:8.4f} {self.transformation_matrix[0,2]:8.4f}]\n"
        matrix_str += f"[{self.transformation_matrix[1,0]:8.4f} {self.transformation_matrix[1,1]:8.4f} {self.transformation_matrix[1,2]:8.4f}]"
        
        self.matrix_text.setText(matrix_str)
        
        # Update statistics
        self.feature_matches_label.setText(f"Feature Matches: {result.get('feature_matches', 0)}")
        self.match_quality_label.setText(f"Match Quality: {result.get('match_quality', 0.0):.3f}")
        
        corner_offset = result.get('corner_offset', (0.0, 0.0))
        self.corner_offset_label.setText(f"Corner Offset: ({corner_offset[0]:.2f}, {corner_offset[1]:.2f}) μm")
        
        # Enable export button
        self.export_matrix_btn.setEnabled(True)
    
    def export_matrix(self):
        """Export transformation matrix to file."""
        if self.transformation_matrix is None:
            return
        
        filename, _ = QFileDialog.getSaveFileName(
            self, "Export Transformation Matrix", "", "JSON Files (*.json);;All Files (*)"
        )
        
        if filename:
            data = {
                'transformation_matrix': self.transformation_matrix.tolist(),
                'export_timestamp': QTimer.currentDateTime().toString(),
                'matrix_type': 'affine_2x3'
            }
            
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)
            
            QMessageBox.information(self, "Success", f"Matrix exported to {filename}")
    
    def clear_display(self):
        """Clear the display."""
        self.transformation_matrix = None
        self.matrix_text.clear()
        self.feature_matches_label.setText("Feature Matches: 0")
        self.match_quality_label.setText("Match Quality: 0.000")
        self.corner_offset_label.setText("Corner Offset: (0.0, 0.0) μm")
        self.export_matrix_btn.setEnabled(False)


class HybridCornerDebugPanel(QWidget):
    """
    Main debug panel for hybrid corner alignment system.

    This widget combines feature visualization, transformation display,
    and control interfaces following established patterns from the codebase.
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.alignment_system = None

    def setup_ui(self):
        """Setup the user interface."""
        layout = QVBoxLayout(self)

        # Title
        title = QLabel("Hybrid Corner Alignment Debug Panel")
        title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Main splitter
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left panel: Feature visualization
        left_panel = QFrame()
        left_layout = QVBoxLayout(left_panel)

        self.feature_widget = HybridFeatureVisualizationWidget()
        left_layout.addWidget(self.feature_widget)

        main_splitter.addWidget(left_panel)

        # Right panel: Controls and transformation
        right_panel = QFrame()
        right_layout = QVBoxLayout(right_panel)

        # Control buttons
        control_group = QGroupBox("Controls")
        control_layout = QVBoxLayout(control_group)

        self.create_reference_btn = QPushButton("Create Hybrid Reference")
        self.create_reference_btn.clicked.connect(self.create_reference)

        self.load_reference_btn = QPushButton("Load Reference")
        self.load_reference_btn.clicked.connect(self.load_reference)

        self.perform_alignment_btn = QPushButton("Perform Re-alignment")
        self.perform_alignment_btn.clicked.connect(self.perform_alignment)
        self.perform_alignment_btn.setEnabled(False)

        control_layout.addWidget(self.create_reference_btn)
        control_layout.addWidget(self.load_reference_btn)
        control_layout.addWidget(self.perform_alignment_btn)

        right_layout.addWidget(control_group)

        # Transformation display
        self.transformation_widget = TransformationMatrixWidget()
        right_layout.addWidget(self.transformation_widget)

        # Status display
        status_group = QGroupBox("Status")
        status_layout = QVBoxLayout(status_group)

        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        status_layout.addWidget(self.status_text)

        right_layout.addWidget(status_group)

        main_splitter.addWidget(right_panel)

        # Set splitter proportions
        main_splitter.setSizes([600, 400])

        layout.addWidget(main_splitter)

        # Reference data storage
        self.reference_data = None

    def set_alignment_system(self, alignment_system: HybridCornerAlignmentSystem):
        """Set the alignment system instance."""
        self.alignment_system = alignment_system
        # Connect status callback
        self.alignment_system.status_callback = self.add_status_message

    def create_reference(self):
        """Create a new hybrid corner reference."""
        if self.alignment_system is None:
            self.add_status_message("Error: No alignment system configured")
            return

        try:
            self.add_status_message("Creating hybrid corner reference...")

            # In a real implementation, this would:
            # 1. Capture corner image
            # 2. Detect flakes in corner region
            # 3. Call alignment_system.create_hybrid_reference()

            # For demo purposes, show a placeholder
            self.add_status_message("Note: This is a demo implementation")
            self.add_status_message("In practice, would capture corner image and detect features")

            # Enable alignment button
            self.perform_alignment_btn.setEnabled(True)

        except Exception as e:
            self.add_status_message(f"Error creating reference: {str(e)}")

    def load_reference(self):
        """Load an existing hybrid corner reference."""
        filename, _ = QFileDialog.getOpenFileName(
            self, "Load Hybrid Reference", "", "JSON Files (*.json);;All Files (*)"
        )

        if filename:
            self.reference_data = load_hybrid_reference(filename)
            if self.reference_data:
                self.add_status_message(f"Loaded reference: {filename}")
                self.add_status_message(f"Features: {self.reference_data.get('feature_counts', {})}")
                self.perform_alignment_btn.setEnabled(True)
            else:
                self.add_status_message(f"Failed to load reference: {filename}")

    def perform_alignment(self):
        """Perform hybrid corner re-alignment."""
        if self.alignment_system is None or self.reference_data is None:
            self.add_status_message("Error: No alignment system or reference data")
            return

        try:
            self.add_status_message("Performing hybrid corner re-alignment...")

            # In a real implementation, this would call:
            # result = self.alignment_system.perform_hybrid_realignment(self.reference_data)

            # For demo purposes, create a mock result
            mock_result = {
                'success': True,
                'transformation_matrix': [[1.0, 0.0, 5.2], [0.0, 1.0, -3.1]],
                'feature_matches': 8,
                'match_quality': 0.892,
                'corner_offset': (5.2, -3.1)
            }

            if mock_result['success']:
                self.add_status_message("Re-alignment successful!")
                self.transformation_widget.update_transformation(mock_result)
            else:
                self.add_status_message(f"Re-alignment failed: {mock_result.get('error', 'Unknown error')}")

        except Exception as e:
            self.add_status_message(f"Error during re-alignment: {str(e)}")

    def add_status_message(self, message: str):
        """Add a status message to the display."""
        from datetime import datetime
        timestamp = datetime.now().strftime('%H:%M:%S')
        formatted_message = f"[{timestamp}] {message}"

        self.status_text.append(formatted_message)

        # Auto-scroll to bottom
        scrollbar = self.status_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def update_feature_display(self, corner_image: np.ndarray, features: List[HybridFeature]):
        """Update the feature visualization display."""
        self.feature_widget.update_display(corner_image, features)


# Standalone application for testing
def create_hybrid_debug_application():
    """Create a standalone application for testing the hybrid debug panel."""
    from PyQt6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # Create main window
    window = HybridCornerDebugPanel()
    window.setWindowTitle("Hybrid Corner Alignment Debug Panel")
    window.resize(1200, 800)

    # Show window
    window.show()

    return app, window


if __name__ == "__main__":
    app, window = create_hybrid_debug_application()
    sys.exit(app.exec())
