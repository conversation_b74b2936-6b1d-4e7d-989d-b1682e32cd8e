================================================================================
SCANNING OPERATION LOG - 2025-07-08 18:55:30
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1751972127\scan_1751972127_flake_data.csv
Grid Steps: 0 x 0
Edge Method: general
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1751972127
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1751972127\debug_screenshots
================================================================================

[2025-07-08 18:55:30.820] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-08 18:55:30.830] [INFO] [SYSTEM] Using custom scan folder: scan_1751972127
[2025-07-08 18:55:30.850] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-08 18:55:31.001] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-08 18:55:31.017] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-08 18:55:31.026] [INFO] [STATUS] ✗ SCAN ABORTED: Camera not available - PyNikonSciCam not installed or camera not detected
[2025-07-08 18:55:31.037] [INFO] [STATUS] ✗ Cannot proceed without a valid hybrid corner reference
[2025-07-08 18:55:31.049] [INFO] [STATUS] ✗ Please check:
[2025-07-08 18:55:31.071] [INFO] [STATUS]   → Chip positioning and visibility
[2025-07-08 18:55:31.081] [INFO] [STATUS]   → Edge detection parameters
[2025-07-08 18:55:31.090] [INFO] [STATUS]   → Image quality and lighting
[2025-07-08 18:55:31.099] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1751972127\scan_1751972127_flake_data.csv
[2025-07-08 18:55:31.109] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-08 18:55:31.119] [INFO] [STATUS] Scanning for annotated images...
[2025-07-08 18:55:31.142] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
