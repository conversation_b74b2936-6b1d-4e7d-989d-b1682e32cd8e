================================================================================
SCANNING OPERATION LOG - 2025-07-09 19:31:07
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752060665\scan_1752060665_flake_data.csv
Grid Steps: 0 x 0
Edge Method: background
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752060665
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752060665\debug_screenshots
================================================================================

[2025-07-09 19:31:07.610] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-09 19:31:07.621] [INFO] [SYSTEM] Using custom scan folder: scan_1752060665
[2025-07-09 19:31:07.636] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-09 19:31:07.753] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-09 19:31:07.774] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-09 19:31:07.784] [INFO] [STATUS] ✗ SCAN ABORTED: Unexpected error in corner detection: 'CentralizedCameraManager' object has no attribute 'get_image'
[2025-07-09 19:31:07.795] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1752060665\scan_1752060665_flake_data.csv
[2025-07-09 19:31:07.808] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-09 19:31:07.829] [INFO] [STATUS] Scanning for annotated images...
[2025-07-09 19:31:07.840] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
