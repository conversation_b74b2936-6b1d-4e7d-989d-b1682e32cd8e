================================================================================
SCANNING OPERATION LOG - 2025-07-09 11:35:49
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752032146\scan_1752032146_flake_data.csv
Grid Steps: 0 x 0
Edge Method: background
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752032146
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752032146\debug_screenshots
================================================================================

[2025-07-09 11:35:49.894] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-09 11:35:49.905] [INFO] [SYSTEM] Using custom scan folder: scan_1752032146
[2025-07-09 11:35:49.925] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-09 11:35:50.068] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-09 11:35:50.080] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-09 11:35:50.091] [ERROR] [ERROR] ✗ SCAN ABORTED: Camera capture failed: Failed to send command. Error code: -10
[2025-07-09 11:35:50.101] [INFO] [STATUS] ✗ Cannot proceed without a valid hybrid corner reference
[2025-07-09 11:35:50.114] [INFO] [STATUS] ✗ Please check:
[2025-07-09 11:35:50.126] [INFO] [STATUS]   → Chip positioning and visibility
[2025-07-09 11:35:50.142] [INFO] [STATUS]   → Edge detection parameters
[2025-07-09 11:35:50.153] [INFO] [STATUS]   → Image quality and lighting
[2025-07-09 11:35:50.164] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1752032146\scan_1752032146_flake_data.csv
[2025-07-09 11:35:50.173] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-09 11:35:50.188] [INFO] [STATUS] Scanning for annotated images...
[2025-07-09 11:35:50.208] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
