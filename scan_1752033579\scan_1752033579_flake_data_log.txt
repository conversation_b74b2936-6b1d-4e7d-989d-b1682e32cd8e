================================================================================
SCANNING OPERATION LOG - 2025-07-09 11:59:44
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752033579\scan_1752033579_flake_data.csv
Grid Steps: 0 x 0
Edge Method: general
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752033579
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752033579\debug_screenshots
================================================================================

[2025-07-09 11:59:44.794] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-09 11:59:44.805] [INFO] [SYSTEM] Using custom scan folder: scan_1752033579
[2025-07-09 11:59:44.822] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-09 11:59:44.978] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-09 11:59:44.988] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-09 11:59:45.082] [ERROR] [ERROR] ✗ SCAN ABORTED: Camera capture failed: Failed to send command. Error code: -10
[2025-07-09 11:59:45.098] [INFO] [STATUS] ✗ Cannot proceed without a valid hybrid corner reference
[2025-07-09 11:59:45.110] [INFO] [STATUS] ✗ Please check:
[2025-07-09 11:59:45.120] [INFO] [STATUS]   → Chip positioning and visibility
[2025-07-09 11:59:45.130] [INFO] [STATUS]   → Edge detection parameters
[2025-07-09 11:59:45.141] [INFO] [STATUS]   → Image quality and lighting
[2025-07-09 11:59:45.154] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1752033579\scan_1752033579_flake_data.csv
[2025-07-09 11:59:45.165] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-09 11:59:45.174] [INFO] [STATUS] Scanning for annotated images...
[2025-07-09 11:59:45.185] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
