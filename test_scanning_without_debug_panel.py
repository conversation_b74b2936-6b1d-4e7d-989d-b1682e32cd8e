#!/usr/bin/env python3
"""
Test script to verify scanning functionality works without debug panel dependencies.

This script tests that:
1. ScanWorker can be instantiated without debug_panel parameters
2. Scanning process can start without debug panel related errors
3. All core scanning functionality is preserved
4. No debug panel imports or references cause issues
"""

import sys
import os
import time
import tempfile
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

def test_scanworker_instantiation():
    """Test that ScanWorker can be instantiated without debug_panel dependencies."""
    print("Testing ScanWorker instantiation...")
    
    try:
        from scanning import Scan<PERSON>orker
        from config import DEFAULT_REGION
        
        # Create a temporary output file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as temp_file:
            temp_csv = temp_file.name
        
        # Test ScanWorker instantiation with various parameter combinations
        test_cases = [
            {
                'name': 'Basic Grid Scan',
                'params': {
                    'mode': 'grid',
                    'x_steps': 2,
                    'y_steps': 2,
                    'out_csv': temp_csv,
                    'region': DEFAULT_REGION
                }
            },
            {
                'name': 'Adaptive Scan with Edge Detection',
                'params': {
                    'mode': 'adaptive',
                    'x_steps': 0,
                    'y_steps': 0,
                    'out_csv': temp_csv,
                    'region': DEFAULT_REGION,
                    'edge_method': 'background'
                }
            },
            {
                'name': 'Scan with Hybrid Alignment',
                'params': {
                    'mode': 'adaptive',
                    'x_steps': 0,
                    'y_steps': 0,
                    'out_csv': temp_csv,
                    'region': DEFAULT_REGION,
                    'enable_hybrid_alignment': True
                }
            },
            {
                'name': 'Scan with All Options',
                'params': {
                    'mode': 'adaptive',
                    'x_steps': 0,
                    'y_steps': 0,
                    'out_csv': temp_csv,
                    'region': DEFAULT_REGION,
                    'edge_margin': 30,
                    'debug': True,
                    'edge_method': 'canny',
                    'enable_hybrid_alignment': True,
                    'scan_folder': 'test_scan'
                }
            }
        ]
        
        all_passed = True
        for test_case in test_cases:
            try:
                worker = ScanWorker(**test_case['params'])
                print(f"✓ {test_case['name']}: ScanWorker instantiated successfully")
                
                # Check that worker has essential attributes
                essential_attrs = ['mode', 'stage', 'camera_manager', 'edge_detector']
                for attr in essential_attrs:
                    if hasattr(worker, attr):
                        print(f"  ✓ Has {attr} attribute")
                    else:
                        print(f"  ✗ Missing {attr} attribute")
                        all_passed = False
                        
                # Clean up worker
                if hasattr(worker, 'stage'):
                    worker.stage.close()
                    
            except Exception as e:
                print(f"✗ {test_case['name']}: Failed to instantiate ScanWorker: {e}")
                all_passed = False
        
        # Clean up temp file
        try:
            os.unlink(temp_csv)
        except:
            pass
            
        return all_passed
        
    except Exception as e:
        print(f"✗ ScanWorker instantiation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_no_debug_panel_references():
    """Test that there are no debug_panel references in the scanning module."""
    print("\nTesting for debug_panel references...")
    
    try:
        import scanning
        import inspect
        
        # Get the source code of the scanning module
        source_code = inspect.getsource(scanning)
        
        # Check for debug_panel references
        debug_panel_refs = []
        lines = source_code.split('\n')
        for i, line in enumerate(lines, 1):
            if 'debug_panel' in line.lower():
                debug_panel_refs.append(f"Line {i}: {line.strip()}")
        
        if debug_panel_refs:
            print("✗ Found debug_panel references in scanning.py:")
            for ref in debug_panel_refs:
                print(f"  {ref}")
            return False
        else:
            print("✓ No debug_panel references found in scanning.py")
            return True
            
    except Exception as e:
        print(f"✗ Debug panel reference test failed: {e}")
        return False


def test_scanworker_parameters():
    """Test ScanWorker parameter signature to ensure debug_panel is removed."""
    print("\nTesting ScanWorker parameter signature...")
    
    try:
        from scanning import ScanWorker
        import inspect
        
        # Get the __init__ method signature
        sig = inspect.signature(ScanWorker.__init__)
        params = list(sig.parameters.keys())
        
        print(f"ScanWorker parameters: {params}")
        
        # Check that debug_panel is not in parameters
        if 'debug_panel' in params:
            print("✗ debug_panel parameter still exists in ScanWorker.__init__")
            return False
        else:
            print("✓ debug_panel parameter successfully removed from ScanWorker.__init__")
            
        # Check that essential parameters are still present
        essential_params = ['mode', 'x_steps', 'y_steps', 'out_csv', 'region']
        missing_params = []
        for param in essential_params:
            if param not in params:
                missing_params.append(param)
                
        if missing_params:
            print(f"✗ Missing essential parameters: {missing_params}")
            return False
        else:
            print("✓ All essential parameters are present")
            
        return True
        
    except Exception as e:
        print(f"✗ ScanWorker parameter test failed: {e}")
        return False


def test_main_app_scan_functionality():
    """Test that MainApp can create ScanWorker without debug_panel issues."""
    print("\nTesting MainApp scan functionality...")
    
    try:
        from ui_components import MainApp
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            
        # Create MainApp instance
        main_app = MainApp()
        
        # Check that scan method exists
        if hasattr(main_app, 'scan'):
            print("✓ MainApp has scan method")
        else:
            print("✗ MainApp missing scan method")
            return False
            
        # Check that essential UI components exist
        essential_components = ['mode_combo', 'edge_method_combo', 'pb', 'lb']
        missing_components = []
        for component in essential_components:
            if not hasattr(main_app, component):
                missing_components.append(component)
                
        if missing_components:
            print(f"✗ Missing essential UI components: {missing_components}")
            return False
        else:
            print("✓ All essential UI components are present")
            
        # Test that we can access the scan method without errors
        try:
            # We won't actually call scan() as it requires user input,
            # but we can check that the method is callable
            scan_method = getattr(main_app, 'scan')
            if callable(scan_method):
                print("✓ Scan method is callable")
            else:
                print("✗ Scan method is not callable")
                return False
        except Exception as e:
            print(f"✗ Error accessing scan method: {e}")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ MainApp scan functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_imports_without_debug_dependencies():
    """Test that all scanning-related imports work without debug dependencies."""
    print("\nTesting imports without debug dependencies...")
    
    try:
        # Test core scanning imports
        from scanning import ScanWorker, StageController, Flake
        print("✓ Core scanning imports successful")
        
        # Test UI imports
        from ui_components import MainApp, VideoStreamingPanel
        print("✓ UI component imports successful")
        
        # Test camera manager imports
        from camera_manager import CameraManager, VideoStreamingManager
        print("✓ Camera manager imports successful")
        
        # Test edge detection imports
        from edge_detection import EdgeDetector, BackgroundEdgeDetector, CannyEdgeDetector
        print("✓ Edge detection imports successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests to verify scanning works without debug panel dependencies."""
    print("=" * 60)
    print("Scanning Without Debug Panel Test Suite")
    print("=" * 60)
    
    tests = [
        ("Import Tests", test_imports_without_debug_dependencies),
        ("ScanWorker Parameter Signature", test_scanworker_parameters),
        ("Debug Panel Reference Check", test_no_debug_panel_references),
        ("ScanWorker Instantiation", test_scanworker_instantiation),
        ("MainApp Scan Functionality", test_main_app_scan_functionality),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test {test_name} crashed: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'=' * 60}")
    print("Test Results Summary")
    print(f"{'=' * 60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{test_name:<35} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Scanning functionality works without debug panel dependencies.")
        print("\nKey verifications:")
        print("  ✓ ScanWorker can be instantiated without debug_panel parameter")
        print("  ✓ No debug_panel references remain in scanning.py")
        print("  ✓ MainApp can create ScanWorker instances")
        print("  ✓ All essential scanning functionality is preserved")
        print("  ✓ No import errors related to debug panels")
    else:
        print("⚠ Some tests failed. Please check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
