================================================================================
SCANNING OPERATION LOG - 2025-07-09 19:29:08
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752060545\scan_1752060545_flake_data.csv
Grid Steps: 0 x 0
Edge Method: general
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752060545
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752060545\debug_screenshots
================================================================================

[2025-07-09 19:29:08.701] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-09 19:29:08.711] [INFO] [SYSTEM] Using custom scan folder: scan_1752060545
[2025-07-09 19:29:08.728] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-09 19:29:08.837] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-09 19:29:08.848] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-09 19:29:08.910] [ERROR] [ERROR] ✗ SCAN ABORTED: Camera capture failed: Failed to send command. Error code: -10
[2025-07-09 19:29:08.920] [INFO] [STATUS] ✗ Cannot proceed without a valid hybrid corner reference
[2025-07-09 19:29:08.930] [INFO] [STATUS] ✗ Please check:
[2025-07-09 19:29:08.940] [INFO] [STATUS]   → Chip positioning and visibility
[2025-07-09 19:29:08.950] [INFO] [STATUS]   → Edge detection parameters
[2025-07-09 19:29:08.960] [INFO] [STATUS]   → Image quality and lighting
[2025-07-09 19:29:08.970] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1752060545\scan_1752060545_flake_data.csv
[2025-07-09 19:29:08.980] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-09 19:29:08.989] [INFO] [STATUS] Scanning for annotated images...
[2025-07-09 19:29:09.003] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
