#!/usr/bin/env python3
"""
Test script to verify the centralized camera access manager resolves concurrent access issues.

This script tests that:
1. CentralizedCameraManager properly coordinates between streaming and scanning operations
2. Video streaming can be paused/resumed when scanning operations need camera access
3. Multiple components can safely access the camera through the centralized manager
4. No "Camera not available" errors occur during concurrent access attempts
5. The singleton pattern ensures only one camera connection exists
"""

import sys
import time
import threading
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

def test_centralized_manager_singleton():
    """Test that CentralizedCameraManager implements singleton pattern correctly."""
    print("Testing CentralizedCameraManager singleton pattern...")
    
    try:
        from camera_manager import CentralizedCameraManager
        
        # Create multiple instances and verify they're the same object
        manager1 = CentralizedCameraManager(camera_index=0)
        manager2 = CentralizedCameraManager(camera_index=0)
        manager3 = CentralizedCameraManager(camera_index=3)  # Different camera index
        
        if manager1 is manager2:
            print("✓ Multiple instances return the same object (singleton working)")
        else:
            print("✗ Multiple instances are different objects (singleton failed)")
            return False
            
        if manager1 is manager3:
            print("✓ Different camera indices still return same singleton instance")
        else:
            print("⚠ Different camera indices create different instances (expected behavior)")
            
        return True
        
    except Exception as e:
        print(f"✗ Singleton test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_camera_manager_compatibility():
    """Test that legacy CameraManager still works with centralized access."""
    print("\nTesting CameraManager compatibility...")
    
    try:
        from camera_manager import CameraManager, CAMERA_AVAILABLE
        
        if not CAMERA_AVAILABLE:
            print("⚠ PyNikonSciCam not available - skipping camera tests")
            return True
            
        # Test legacy CameraManager creation
        camera_manager = CameraManager(camera_index=3, test_camera=False)  # Use simulator
        print("✓ Legacy CameraManager created successfully")
        
        # Test that it uses centralized manager internally
        if hasattr(camera_manager, 'centralized_manager'):
            print("✓ CameraManager uses centralized manager internally")
        else:
            print("✗ CameraManager missing centralized manager")
            return False
            
        # Test image capture
        try:
            img = camera_manager.get_image()
            if img is not None:
                print(f"✓ Image capture successful, shape: {img.shape}")
            else:
                print("⚠ Image capture returned None (expected if no camera)")
        except Exception as e:
            print(f"⚠ Image capture failed (expected if no camera): {e}")
            
        return True
        
    except Exception as e:
        print(f"✗ CameraManager compatibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_streaming_and_scanning_coordination():
    """Test coordination between streaming and scanning operations."""
    print("\nTesting streaming and scanning coordination...")
    
    try:
        from camera_manager import CentralizedCameraManager, VideoStreamingManager, CAMERA_AVAILABLE
        
        if not CAMERA_AVAILABLE:
            print("⚠ PyNikonSciCam not available - skipping coordination tests")
            return True
            
        # Get centralized manager
        centralized_manager = CentralizedCameraManager(camera_index=3)  # Use simulator
        
        # Test streaming start/stop
        streaming_started = centralized_manager.start_streaming()
        if streaming_started:
            print("✓ Streaming started successfully")
            
            # Test that we can still capture images (should pause/resume streaming)
            try:
                img = centralized_manager.get_image()
                if img is not None:
                    print("✓ Image capture during streaming successful")
                else:
                    print("⚠ Image capture during streaming returned None")
            except Exception as e:
                print(f"⚠ Image capture during streaming failed: {e}")
                
            # Test parameter setting during streaming
            success = centralized_manager.set_camera_parameter("exposure_time", 100)
            if success:
                print("✓ Parameter setting during streaming successful")
            else:
                print("⚠ Parameter setting during streaming failed")
                
            # Stop streaming
            centralized_manager.stop_streaming()
            print("✓ Streaming stopped successfully")
            
        else:
            print("⚠ Streaming failed to start (expected if no camera)")
            
        return True
        
    except Exception as e:
        print(f"✗ Streaming and scanning coordination test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_concurrent_access_simulation():
    """Simulate concurrent access from multiple threads."""
    print("\nTesting concurrent access simulation...")
    
    try:
        from camera_manager import CentralizedCameraManager, CAMERA_AVAILABLE
        
        if not CAMERA_AVAILABLE:
            print("⚠ PyNikonSciCam not available - skipping concurrent access tests")
            return True
            
        centralized_manager = CentralizedCameraManager(camera_index=3)  # Use simulator
        
        # Results tracking
        results = {'streaming_images': 0, 'scan_images': 0, 'errors': 0}
        results_lock = threading.Lock()
        
        def streaming_thread():
            """Simulate continuous streaming."""
            try:
                centralized_manager.start_streaming()
                for i in range(10):
                    img = centralized_manager.get_streaming_image()
                    with results_lock:
                        if img is not None:
                            results['streaming_images'] += 1
                    time.sleep(0.1)
            except Exception as e:
                with results_lock:
                    results['errors'] += 1
                print(f"Streaming thread error: {e}")
            finally:
                centralized_manager.stop_streaming()
        
        def scanning_thread():
            """Simulate periodic scanning operations."""
            try:
                for i in range(5):
                    time.sleep(0.2)  # Wait a bit between scans
                    img = centralized_manager.get_image()
                    with results_lock:
                        if img is not None:
                            results['scan_images'] += 1
            except Exception as e:
                with results_lock:
                    results['errors'] += 1
                print(f"Scanning thread error: {e}")
        
        # Start both threads
        stream_thread = threading.Thread(target=streaming_thread)
        scan_thread = threading.Thread(target=scanning_thread)
        
        stream_thread.start()
        scan_thread.start()
        
        # Wait for completion
        stream_thread.join()
        scan_thread.join()
        
        # Check results
        print(f"✓ Concurrent access test completed:")
        print(f"  - Streaming images captured: {results['streaming_images']}")
        print(f"  - Scan images captured: {results['scan_images']}")
        print(f"  - Errors encountered: {results['errors']}")
        
        if results['errors'] == 0:
            print("✓ No errors during concurrent access")
            return True
        else:
            print(f"⚠ {results['errors']} errors during concurrent access")
            return False
            
    except Exception as e:
        print(f"✗ Concurrent access simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_video_streaming_manager_integration():
    """Test VideoStreamingManager integration with centralized access."""
    print("\nTesting VideoStreamingManager integration...")
    
    try:
        from camera_manager import VideoStreamingManager, CAMERA_AVAILABLE
        
        if not CAMERA_AVAILABLE:
            print("⚠ PyNikonSciCam not available - skipping streaming manager tests")
            return True
            
        # Create streaming manager
        streaming_manager = VideoStreamingManager(camera_index=3)  # Use simulator
        print("✓ VideoStreamingManager created successfully")
        
        # Test that it uses centralized manager
        if hasattr(streaming_manager, 'centralized_manager'):
            print("✓ VideoStreamingManager uses centralized manager")
        else:
            print("✗ VideoStreamingManager missing centralized manager")
            return False
            
        # Test parameter methods
        params = streaming_manager.get_camera_parameters()
        if params:
            print(f"✓ Camera parameters retrieved: {list(params.keys())}")
        else:
            print("⚠ Camera parameters not available")
            
        # Test parameter setting
        success = streaming_manager.set_camera_parameter("exposure_time", 150)
        if success:
            print("✓ Parameter setting through streaming manager successful")
        else:
            print("⚠ Parameter setting through streaming manager failed")
            
        return True
        
    except Exception as e:
        print(f"✗ VideoStreamingManager integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_scanning_integration():
    """Test that scanning operations work with centralized camera access."""
    print("\nTesting scanning integration...")
    
    try:
        from scanning import ScanWorker
        from camera_manager import CAMERA_AVAILABLE
        import tempfile
        
        if not CAMERA_AVAILABLE:
            print("⚠ PyNikonSciCam not available - skipping scanning integration tests")
            return True
            
        # Create temporary output file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as temp_file:
            temp_csv = temp_file.name
        
        try:
            # Test ScanWorker creation (should use CameraManager which uses centralized access)
            worker = ScanWorker(
                mode='grid',
                x_steps=1,
                y_steps=1,
                out_csv=temp_csv,
                region={'top': 0, 'left': 0, 'width': 800, 'height': 600}
            )
            print("✓ ScanWorker created successfully with centralized camera access")
            
            # Check that camera manager uses centralized access
            if hasattr(worker.camera_manager, 'centralized_manager'):
                print("✓ ScanWorker's CameraManager uses centralized access")
            else:
                print("⚠ ScanWorker's CameraManager may not use centralized access")
                
            # Clean up
            worker.stage.close()
            
        finally:
            import os
            try:
                os.unlink(temp_csv)
            except:
                pass
                
        return True
        
    except Exception as e:
        print(f"✗ Scanning integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests for centralized camera access."""
    print("=" * 60)
    print("Centralized Camera Access Test Suite")
    print("=" * 60)
    
    tests = [
        ("Singleton Pattern", test_centralized_manager_singleton),
        ("CameraManager Compatibility", test_camera_manager_compatibility),
        ("Streaming and Scanning Coordination", test_streaming_and_scanning_coordination),
        ("Concurrent Access Simulation", test_concurrent_access_simulation),
        ("VideoStreamingManager Integration", test_video_streaming_manager_integration),
        ("Scanning Integration", test_scanning_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test {test_name} crashed: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'=' * 60}")
    print("Test Results Summary")
    print(f"{'=' * 60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{test_name:<35} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Centralized camera access is working correctly.")
        print("\nKey verifications:")
        print("  ✓ Singleton pattern ensures single camera connection")
        print("  ✓ Legacy CameraManager maintains compatibility")
        print("  ✓ Streaming and scanning operations coordinate properly")
        print("  ✓ Concurrent access is handled without conflicts")
        print("  ✓ VideoStreamingManager integrates with centralized access")
        print("  ✓ Scanning operations work with shared camera instance")
    else:
        print("⚠ Some tests failed. Please check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
