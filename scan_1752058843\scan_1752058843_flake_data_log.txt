================================================================================
SCANNING OPERATION LOG - 2025-07-09 19:00:46
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752058843\scan_1752058843_flake_data.csv
Grid Steps: 0 x 0
Edge Method: background
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752058843
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752058843\debug_screenshots
================================================================================

[2025-07-09 19:00:46.988] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-09 19:00:46.998] [INFO] [SYSTEM] Using custom scan folder: scan_1752058843
[2025-07-09 19:00:47.013] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-09 19:00:47.175] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-09 19:00:47.185] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-09 19:00:51.827] [ERROR] [ERROR] ✗ SCAN ABORTED: Camera capture failed after 4 attempts: Failed to send command. Error code: -10
[2025-07-09 19:00:51.840] [INFO] [STATUS] ✗ Cannot proceed without a valid hybrid corner reference
[2025-07-09 19:00:51.849] [INFO] [STATUS] ✗ Please check:
[2025-07-09 19:00:51.859] [INFO] [STATUS]   → Chip positioning and visibility
[2025-07-09 19:00:51.868] [INFO] [STATUS]   → Edge detection parameters
[2025-07-09 19:00:51.878] [INFO] [STATUS]   → Image quality and lighting
[2025-07-09 19:00:51.888] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1752058843\scan_1752058843_flake_data.csv
[2025-07-09 19:00:51.898] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-09 19:00:51.907] [INFO] [STATUS] Scanning for annotated images...
[2025-07-09 19:00:51.920] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
