================================================================================
SCANNING OPERATION LOG - 2025-07-08 18:43:19
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1751971396\scan_1751971396_flake_data.csv
Grid Steps: 0 x 0
Edge Method: background
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1751971396
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1751971396\debug_screenshots
================================================================================

[2025-07-08 18:43:19.609] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-08 18:43:19.620] [INFO] [SYSTEM] Using custom scan folder: scan_1751971396
[2025-07-08 18:43:19.640] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-08 18:43:19.776] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-08 18:43:19.787] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-08 18:43:19.796] [INFO] [STATUS] ✗ SCAN ABORTED: Camera not available - PyNikonSciCam not installed or camera not detected
[2025-07-08 18:43:19.806] [INFO] [STATUS] ✗ Cannot proceed without a valid hybrid corner reference
[2025-07-08 18:43:19.830] [INFO] [STATUS] ✗ Please check:
[2025-07-08 18:43:19.841] [INFO] [STATUS]   → Chip positioning and visibility
[2025-07-08 18:43:19.850] [INFO] [STATUS]   → Edge detection parameters
[2025-07-08 18:43:19.860] [INFO] [STATUS]   → Image quality and lighting
[2025-07-08 18:43:19.870] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1751971396\scan_1751971396_flake_data.csv
[2025-07-08 18:43:19.880] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-08 18:43:19.896] [INFO] [STATUS] Scanning for annotated images...
[2025-07-08 18:43:19.906] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
