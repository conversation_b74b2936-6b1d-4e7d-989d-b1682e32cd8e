"""
Scanning Module for Stage Control and Scanning Operations

This module contains classes for controlling the microscope stage and performing
grid/adaptive scanning operations with enhanced rotation robustness.
"""

import csv
import time
import numpy as np
import cv2
import os
import json
import logging
import threading
from datetime import datetime
from typing import List, Dict, Optional
from PyQt6.QtCore import QThread, pyqtSignal
from MCM301_COMMAND_LIB import MCM301
from inference_sdk import InferenceHTTPClient
import supervision as sv

# Camera availability is now handled in camera_manager.py

from config import (STEP_Y_UM, STEP_X_UM, DETECTION_API_URL, DETECTION_API_KEY,
                   DETECTION_MODEL_ID, SELECTED_CLASSES)
from edge_detection import EdgeDetector, BackgroundEdgeDetector, NoEdgeDetector, CannyEdgeDetector
from hybrid_corner_alignment import (
    HybridCornerAlignmentSystem, save_hybrid_reference, load_hybrid_reference,
    create_hybrid_reference_filename
)
from camera_manager import CentralizedCameraManager


# Simple Flake class for scanning workflow
class Flake:
    """Simple flake data structure for scanning workflow"""
    def __init__(self, id, center_x, center_y, real_x_um, real_y_um, shape, class_name):
        self.id = id
        self.center_x = center_x
        self.center_y = center_y
        self.real_x_um = real_x_um
        self.real_y_um = real_y_um
        self.shape = shape
        self.class_name = class_name
        self.area = 0  # Default value


def calculate_chip_bounds(flakes):
    """Calculate chip bounds from flake coordinates"""
    if not flakes:
        return {'min_x': 0, 'max_x': 0, 'min_y': 0, 'max_y': 0}

    x_coords = [flake.real_x_um for flake in flakes]
    y_coords = [flake.real_y_um for flake in flakes]

    return {
        'min_x': min(x_coords),
        'max_x': max(x_coords),
        'min_y': min(y_coords),
        'max_y': max(y_coords)
    }


class StageController:
    """Enhanced stage controller with improved error handling"""

    def __init__(self, status_callback=None):
        self.dev = MCM301()
        devs = MCM301.list_devices()
        if not devs:
            raise RuntimeError("No stage found")
        sn = devs[0][0]
        if self.dev.open(sn, 115200, 3) < 0 or not self.dev.is_open(sn):
            raise RuntimeError("Cannot open stage")
        self.is_connected = True
        self.status_callback = status_callback

    def move_absolute(self, y_um, x_um):
        """
        Move to absolute position - Y is horizontal, X is vertical
        Enhanced with error checking and real-time position feedback
        """
        if not self.is_connected:
            raise RuntimeError("Stage not connected")

        try:
            ctry = [0]
            self.dev.convert_nm_to_encoder(5, y_um * 1000, ctry)
            ctrx = [0]
            self.dev.convert_nm_to_encoder(4, x_um * 1000, ctrx)
            self.dev.move_absolute(5, ctry[0])
            self.dev.move_absolute(4, ctrx[0])

            # Get real-time position feedback after movement
            actual_y, actual_x = self.get_position()

            # Emit position feedback if callback is available
            if self.status_callback:
                try:
                    self.status_callback(f"Position feedback: ({actual_y:.2f}, {actual_x:.2f}) μm")
                except Exception:
                    pass  # Don't fail movement if status callback fails

            # Optional: Check if movement was accurate (within tolerance)
            # tolerance_um = 1.0  # 1 micrometer tolerance
            # y_error = abs(actual_y - y_um)
            # x_error = abs(actual_x - x_um)

            # if y_error > tolerance_um or x_error > tolerance_um:
            #     # Log warning but don't fail - some drift is expected
            #     if self.status_callback:
            #         try:
            #             self.status_callback(f"⚠ Movement accuracy: Y error={y_error:.2f} μm, X error={x_error:.2f} μm")
            #         except Exception:
            #             pass

        except Exception as e:
            raise RuntimeError(f"Stage movement failed: {str(e)}")

    def get_position(self):
        """
        Get current stage position in micrometers using real-time encoder data.
        Enhanced with get_mot_status() for accurate position feedback.
        """
        if not self.is_connected:
            raise RuntimeError("Stage not connected")

        try:
            # Get real-time position data using get_mot_status
            pos_y = [0]
            status_y = [0]
            pos_x = [0]
            status_x = [0]

            # Get current encoder positions for both axes
            ret_y = self.dev.get_mot_status(5, pos_y, status_y)
            ret_x = self.dev.get_mot_status(4, pos_x, status_x)

            if ret_y < 0 or ret_x < 0:
                raise RuntimeError(f"Failed to get motor status: Y={ret_y}, X={ret_x}")

            # Convert encoder units to nanometers
            y_nm = [0]
            x_nm = [0]
            ret_y_conv = self.dev.convert_encoder_to_nm(5, pos_y[0], y_nm)
            ret_x_conv = self.dev.convert_encoder_to_nm(4, pos_x[0], x_nm)

            if ret_y_conv < 0 or ret_x_conv < 0:
                raise RuntimeError(f"Failed to convert encoder units: Y={ret_y_conv}, X={ret_x_conv}")

            # Convert nanometers to micrometers
            return y_nm[0] / 1000.0, x_nm[0] / 1000.0
        except Exception as e:
            raise RuntimeError(f"Position reading failed: {str(e)}")

    def set_zero(self):
        """
        Reset the stage position reference point to zero using set_MOT_encounter().
        This method sets the current encoder position as the new zero reference point.
        Should be called at scan start and during re-alignment when upper-left corner is found.
        """
        if not self.is_connected:
            raise RuntimeError("Stage not connected")

        try:
            # Set encoder count to zero for both axes (slots 4 and 5)
            # This resets the position reference point to the current location
            ret_y = self.dev.set_MOT_encounter(5, 0)  # Y-axis (horizontal)
            ret_x = self.dev.set_MOT_encounter(4, 0)  # X-axis (vertical)

            if ret_y < 0 or ret_x < 0:
                raise RuntimeError(f"Failed to set zero position: Y={ret_y}, X={ret_x}")

            # Verify the zero position was set correctly
            pos_y, pos_x = self.get_position()

            # The position should now be very close to zero (within tolerance)
            tolerance_um = 0.1  # 0.1 micrometer tolerance
            if abs(pos_y) > tolerance_um or abs(pos_x) > tolerance_um:
                raise RuntimeError(f"Zero position verification failed: Y={pos_y:.3f}μm, X={pos_x:.3f}μm")

            return True
        except Exception as e:
            raise RuntimeError(f"Set zero failed: {str(e)}")

    def close(self):
        """Close stage connection"""
        if self.is_connected:
            try:
                self.dev.close()
                self.is_connected = False
            except:
                pass  # Ignore errors during cleanup


class ScanWorker(QThread):
    """
    Enhanced scanner worker thread with improved rotation robustness.
    Handles both grid and adaptive scanning modes.
    """
    progress = pyqtSignal(int, int)
    finished = pyqtSignal(str)
    status = pyqtSignal(str)

    def __init__(self, mode, x_steps, y_steps, out_csv, region,
                 edge_margin=20, debug=False, edge_method='background',
                 enable_hybrid_alignment=False, reference_file=None,
                 scan_folder=None):
        super().__init__()
        self.mode = mode
        self.x_steps = x_steps
        self.y_steps = y_steps
        self.out_csv = out_csv
        self.region = region
        self.edge_margin = edge_margin
        self.stage = StageController(status_callback=self._emit_and_log_status)



        # Initialize detection client
        self.detection_client = InferenceHTTPClient(
            api_url=DETECTION_API_URL,
            api_key=DETECTION_API_KEY
        )
        self.polygon_annotator = sv.PolygonAnnotator()
        self.label_annotator = sv.LabelAnnotator()

        # Initialize edge detector with enhanced rotation robustness
        self.edge_method = edge_method  # Store for reference saving
        if mode == 'adaptive':
            if edge_method == 'background':
                self.edge_detector = BackgroundEdgeDetector(debug=debug)
            elif edge_method == 'canny':
                self.edge_detector = CannyEdgeDetector(debug=debug)
            elif edge_method == 'none':
                self.edge_detector = NoEdgeDetector(debug=debug)
            else:
                self.edge_detector = EdgeDetector(debug=debug)
        else:
            self.edge_detector = None

        # Custom scan folder for organized output (initialize first)
        self.scan_folder = scan_folder
        self.debug_screenshots_folder = None
        if scan_folder:
            self.debug_screenshots_folder = os.path.join(scan_folder, 'debug_screenshots')

        # Initialize centralized camera manager for coordinated camera access
        self.camera_manager = CentralizedCameraManager(camera_index=0)

        # Initialize hybrid corner alignment system
        self.enable_hybrid_alignment = enable_hybrid_alignment
        self.reference_file = reference_file
        self.hybrid_alignment_system = None
        if enable_hybrid_alignment:
            # Initialize hybrid corner alignment system
            self.hybrid_alignment_system = HybridCornerAlignmentSystem(
                stage_controller=self.stage,
                region=region,
                status_callback=self._emit_and_log_status,
                debug=debug
            )
            # Pass debug screenshots folder to alignment system
            if self.debug_screenshots_folder:
                self.hybrid_alignment_system.debug_screenshots_folder = self.debug_screenshots_folder

        # Storage for detected flakes (for hybrid corner alignment)
        self.detected_flakes = []
        self.chip_bounds = None

        self.current_origin_x = 0
        self.current_origin_y = 0

        # Initialize comprehensive logging system
        self._log_lock = threading.Lock()  # Thread-safe logging
        self._setup_logging()

    def _ensure_streaming_active(self):
        """Ensure camera streaming is active for the scanning workflow."""
        try:
            if not self.camera_manager.streaming_active:
                self._emit_and_log_status("Starting camera streaming for scanning workflow...")
                success = self.camera_manager.start_streaming()
                if success:
                    self._emit_and_log_status("✓ Camera streaming started successfully")
                    # Brief delay to allow streaming to stabilize
                    time.sleep(1.0)
                else:
                    self._emit_and_log_status("✗ Failed to start camera streaming")
                    return False
            else:
                self._emit_and_log_status("✓ Camera streaming already active")
            return True
        except Exception as e:
            self._emit_and_log_status(f"✗ Error ensuring streaming active: {e}")
            return False

    def _get_streaming_image_with_retry(self, max_retries=3, retry_delay=0.5):
        """
        Get streaming image with retry logic for robustness.

        Args:
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds

        Returns:
            numpy.ndarray or None: Captured image
        """
        for attempt in range(max_retries + 1):
            try:
                img = self.camera_manager.get_streaming_image()
                if img is not None:
                    return img
                else:
                    if attempt < max_retries:
                        self._emit_and_log_status(f"⚠ Streaming image returned None, retrying ({attempt + 1}/{max_retries})...")
                        time.sleep(retry_delay)
                    else:
                        self._emit_and_log_status("✗ Failed to get streaming image after all retries")
            except Exception as e:
                if attempt < max_retries:
                    self._emit_and_log_status(f"⚠ Streaming image error, retrying ({attempt + 1}/{max_retries}): {e}")
                    time.sleep(retry_delay)
                else:
                    self._emit_and_log_status(f"✗ Failed to get streaming image: {e}")
        return None

    def _emit_and_log_status(self, message: str):
        """
        Enhanced status emission that both emits the signal and logs the message.
        This replaces the direct self.status.emit calls for comprehensive logging.
        """
        # Determine message category and level
        category = "STATUS"
        level = "INFO"

        if "Position feedback:" in message:
            category = "POSITION"
        elif "Movement accuracy:" in message:
            category = "MOVEMENT"
            level = "WARNING"
        elif "Error" in message or "Failed" in message:
            category = "ERROR"
            level = "ERROR"
        elif "Warning" in message or "⚠" in message:
            category = "WARNING"
            level = "WARNING"
        elif "✓" in message or "Success" in message:
            category = "SUCCESS"
        elif "Setting" in message and "zero reference" in message:
            category = "CALIBRATION"
        elif "Finding" in message or "Starting" in message:
            category = "WORKFLOW"

        # Log the message
        self._log_message(category, message, level)

        # Emit the signal for GUI display
        self.status.emit(message)

    def _setup_logging(self):
        """Initialize the comprehensive logging system for the scanning workflow."""
        try:
            # Create log file path based on CSV output file
            base_name = os.path.splitext(self.out_csv)[0]
            self.log_file_path = f"{base_name}_log.txt"

            # Initialize log file with header
            with open(self.log_file_path, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write(f"SCANNING OPERATION LOG - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 80 + "\n")
                f.write(f"Mode: {self.mode}\n")
                f.write(f"Output CSV: {self.out_csv}\n")
                f.write(f"Grid Steps: {self.x_steps} x {self.y_steps}\n")
                f.write(f"Edge Method: {getattr(self, 'edge_method', 'N/A')}\n")
                f.write(f"Hybrid Corner Alignment: {self.enable_hybrid_alignment}\n")
                f.write(f"Reference File: {self.reference_file or 'None'}\n")
                if self.scan_folder:
                    f.write(f"Scan Folder: {self.scan_folder}\n")
                    f.write(f"Debug Screenshots: {self.debug_screenshots_folder}\n")
                f.write("=" * 80 + "\n\n")

            self._log_message("SYSTEM", "Logging system initialized successfully")
            if self.scan_folder:
                self._log_message("SYSTEM", f"Using custom scan folder: {os.path.basename(self.scan_folder)}")

        except Exception as e:
            # If logging setup fails, continue without logging
            self.log_file_path = None
            print(f"Warning: Failed to setup logging: {str(e)}")

    def _log_message(self, category: str, message: str, level: str = "INFO"):
        """
        Thread-safe logging method that writes messages to the log file.

        Args:
            category: Message category (STATUS, POSITION, PROGRESS, ERROR, etc.)
            message: The message content
            level: Log level (INFO, WARNING, ERROR)
        """
        if not self.log_file_path:
            return

        try:
            with self._log_lock:
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
                log_entry = f"[{timestamp}] [{level}] [{category}] {message}\n"

                with open(self.log_file_path, 'a', encoding='utf-8') as f:
                    f.write(log_entry)

        except Exception as e:
            # Don't let logging failures interrupt the scanning process
            print(f"Logging error: {str(e)}")

    def _emit_and_log_status(self, message: str):
        """
        Enhanced status emission that both emits the signal and logs the message.
        This replaces the direct self.status.emit calls for comprehensive logging.
        """
        # Determine message category and level
        category = "STATUS"
        level = "INFO"

        if "Position feedback:" in message:
            category = "POSITION"
        elif "Movement accuracy:" in message:
            category = "MOVEMENT"
            level = "WARNING"
        elif "Error" in message or "Failed" in message:
            category = "ERROR"
            level = "ERROR"
        elif "Warning" in message or "⚠" in message:
            category = "WARNING"
            level = "WARNING"
        elif "✓" in message or "Success" in message:
            category = "SUCCESS"
        elif "Setting" in message and "zero reference" in message:
            category = "CALIBRATION"
        elif "Finding" in message or "Starting" in message:
            category = "WORKFLOW"

        # Log the message
        self._log_message(category, message, level)

        # Emit the signal for GUI display
        self.status.emit(message)

    def _log_progress(self, current: int, total: int):
        """Log progress updates with timing information."""
        percentage = (current / total * 100) if total > 0 else 0
        message = f"Progress: {current}/{total} ({percentage:.1f}%)"
        self._log_message("PROGRESS", message)

    def _log_workflow_completion(self, workflow_type: str, success: bool, details: str = ""):
        """Log the completion of a workflow stage."""
        status = "SUCCESS" if success else "FAILURE"
        level = "INFO" if success else "ERROR"
        message = f"{workflow_type} workflow completed - {status}"
        if details:
            message += f" - {details}"
        self._log_message("WORKFLOW", message, level)

        # Add a separator line for major workflow completions
        if workflow_type in ["SCANNING", "RE-ALIGNMENT"]:
            try:
                with self._log_lock:
                    with open(self.log_file_path, 'a', encoding='utf-8') as f:
                        f.write("-" * 80 + "\n")
            except Exception:
                pass

    def get_edge_detector_name(self) -> str:
        """Get the class name of the current edge detector for reference saving"""
        if self.edge_detector is None:
            return "BackgroundEdgeDetector"  # Default for grid mode
        return self.edge_detector.__class__.__name__

    def process_position(self, img, step_id, step_x, step_y, off_x_um, off_y_um, writer):
        """Process current position - detect flakes and save data"""
        try:
            result = self.detection_client.infer(img, model_id=DETECTION_MODEL_ID)
            data = result if isinstance(result, dict) else result.json()

            svd = sv.Detections.from_inference(result)
            svd = svd[np.isin(svd.class_id, list(SELECTED_CLASSES))]
            # annotated = self.polygon_annotator.annotate(scene=img.copy(), detections=svd)
            # annotated = self.label_annotator.annotate(scene=annotated, detections=svd)
            # png_path = f"annot_{step_id}.png"
            # cv2.imwrite(png_path, annotated)

            h, w, _ = img.shape
            origin_x = w // 2
            origin_y = h // 2

            um_per_pixel_vert = STEP_X_UM / h
            um_per_pixel_horiz = STEP_Y_UM / w

            flakes_found = 0
            for pred in data.get('predictions', []):
                if pred['class_id'] in SELECTED_CLASSES:
                    cx, cy = pred['x'], pred['y']
                    pts = [(pt['x'], pt['y']) for pt in pred['points']]

                    dx = cx - origin_x
                    dy = cy - origin_y

                    real_x = off_x_um - dy * um_per_pixel_vert
                    real_y = off_y_um - dx * um_per_pixel_horiz

                    writer.writerow({
                        'step_id': step_id,
                        'step_x': step_x,
                        'step_y': step_y,
                        'pix_origin_x': origin_x,
                        'pix_origin_y': origin_y,
                        'center_x': cx,
                        'center_y': cy,
                        'points': pts,
                        'real_x_um': real_x,
                        'real_y_um': real_y,
                        'class': pred['class'],
                        'detection_id': pred['detection_id']
                    })
                    flakes_found += 1

            return flakes_found

        except Exception as e:
            self._emit_and_log_status(f"Error processing position {step_id}: {str(e)}")
            return 0

    def run_grid_scan(self):
        """Grid scanning method"""
        total = (self.x_steps + 1) * (self.y_steps + 1)
        count = 0

        # Set zero reference point at the very beginning of grid scan workflow
        self._emit_and_log_status("Setting initial zero reference point for grid scan...")
        try:
            self.stage.set_zero()
            self._emit_and_log_status("✓ Initial zero reference point set successfully")
        except Exception as e:
            self._emit_and_log_status(f"⚠ Warning: Failed to set initial zero reference: {str(e)}")
            # Continue with scan even if set_zero fails

        # Create mandatory hybrid corner reference for grid scan
        self._emit_and_log_status("Creating mandatory hybrid corner reference for grid scan...")
        try:
            self._emit_and_log_status("DEBUG: About to call _create_automatic_hybrid_corner_reference()")
            reference_success = self._create_automatic_hybrid_corner_reference()
            self._emit_and_log_status(f"DEBUG: _create_automatic_hybrid_corner_reference() returned: {reference_success}")

            if not reference_success:
                error_msg = "CRITICAL: Failed to create hybrid corner reference. Grid scan cannot proceed."
                self._emit_and_log_status(f"✗ {error_msg}")
                raise RuntimeError(error_msg)
        except RuntimeError:
            raise  # Re-raise RuntimeError to be caught by caller
        except Exception as e:
            error_msg = f"CRITICAL: Unexpected error during reference creation: {str(e)}"
            self._emit_and_log_status(f"✗ {error_msg}")
            raise RuntimeError(error_msg)

        self._emit_and_log_status("DEBUG: Reference creation completed successfully, proceeding to grid scan")
        self._emit_and_log_status("Starting grid scan...")

        with open(self.out_csv, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=[
                'step_id', 'step_x', 'step_y', 'pix_origin_x', 'pix_origin_y',
                'center_x', 'center_y', 'points', 'real_x_um', 'real_y_um',
                'class', 'detection_id'
            ])
            writer.writeheader()

            for j in range(self.x_steps + 1):
                for k in range(self.y_steps + 1):
                    sid = f"{j}-{k}"

                    off_y_um = -j * STEP_Y_UM
                    off_x_um = -k * STEP_X_UM

                    self._emit_and_log_status(f"Moving to grid({j},{k})...")
                    self.stage.move_absolute(off_y_um, off_x_um)

                    # Brief delay after stage movement to ensure stability and avoid motion blur
                    time.sleep(0.3)

                    # Capture image using centralized camera manager with retry
                    img = self._get_streaming_image_with_retry(max_retries=2)

                    flakes = self.process_position(img, sid, j, k, off_x_um, off_y_um, writer)

                    count += 1
                    self.progress.emit(count, total)
                    self._log_progress(count, total)  # Log progress
                    self._emit_and_log_status(f"Grid({j},{k}): Found {flakes} flakes")

    def find_upper_left_corner(self):
        """
        Enhanced corner finding with rotation robustness.
        Find the upper-left corner of the chip using rotation-aware edge detection.
        """
        self._emit_and_log_status("Finding chip upper-left corner...")

        if isinstance(self.edge_detector, NoEdgeDetector):
            self._emit_and_log_status("No edge detection - using current position as origin")
            self.current_origin_x = 0
            self.current_origin_y = 0
            return 0, 0

        # Capture image using centralized camera manager
        img = self.camera_manager.get_streaming_image()

        h, w, _ = img.shape
        center_x, center_y = w // 2, h // 2



        if not self.edge_detector.is_on_chip(img, center_x, center_y):
            self._emit_and_log_status("Not on chip! Please move to chip first.")
            return None

        # Enhanced corner finding for rotated chips
        return self._find_corner_rotation_robust(img, h, w, center_x, center_y)

    def _find_corner_rotation_robust(self, img, h, w, center_x, center_y):
        """
        Find corner using rotation-robust method that follows the sequential workflow.
        This method maintains the same step-by-step process as the original but uses
        rotation-robust edge detection for better accuracy with rotated chips.
        """
        # Step 1: Find left edge (moving in +Y direction) with rotation-robust detection
        self._emit_and_log_status("Finding left edge (rotation-robust)...")
        current_y = 0
        while True:
            # Brief delay after stage movement to avoid motion blur
            time.sleep(0.2)

            # Capture image using centralized camera manager with retry
            img = self._get_streaming_image_with_retry(max_retries=2)



            # Use rotation-robust edge detection with multiple check points
            left_check_points = [
                (int(w * 0.15), center_y),      # 15% from left
                (int(w * 0.2), center_y),       # 20% from left
                (int(w * 0.25), center_y),      # 25% from left
            ]

            # Check if any of the left check points are off the chip
            off_chip_count = 0
            for check_x, check_y in left_check_points:
                if not self.edge_detector.is_on_chip(img, check_x, check_y, margin=self.edge_margin):
                    off_chip_count += 1

            # If majority of check points are off chip, we found the left edge
            if off_chip_count >= len(left_check_points) // 2 + 1:
                self._emit_and_log_status("Found left edge (rotation-robust)")
                # current_y -= STEP_Y_UM * 0.5  # Move back onto chip
                self.stage.move_absolute(current_y, 0)
                time.sleep(0.2)
                break

            # Move left (+Y direction)
            current_y += STEP_Y_UM * 0.5
            self.stage.move_absolute(current_y, 0)
            time.sleep(0.5)

        # Step 2: Find top edge (moving in +X direction) with rotation-robust detection
        self._emit_and_log_status("Finding top edge (rotation-robust)...")
        current_x = 0
        while True:
            # Brief delay after stage movement to avoid motion blur
            time.sleep(0.2)

            # Capture image using centralized camera manager with retry
            img = self._get_streaming_image_with_retry(max_retries=2)



            # Use rotation-robust edge detection with multiple check points
            top_check_points = [
                (center_x, int(h * 0.15)),      # 15% from top
                (center_x, int(h * 0.2)),       # 20% from top
                (center_x, int(h * 0.25)),      # 25% from top
            ]

            # Check if any of the top check points are off the chip
            off_chip_count = 0
            for check_x, check_y in top_check_points:
                if not self.edge_detector.is_on_chip(img, check_x, check_y, margin=self.edge_margin):
                    off_chip_count += 1

            # If majority of check points are off chip, we found the top edge
            if off_chip_count >= len(top_check_points) // 2 + 1:
                self._emit_and_log_status("Found top edge (rotation-robust)")
                # current_x -= STEP_X_UM * 0.5  # Move back onto chip
                self.stage.move_absolute(current_y, current_x)
                time.sleep(0.5)
                break

            # Move up (+X direction)
            current_x += STEP_X_UM * 0.5
            self.stage.move_absolute(current_y, current_x)
            time.sleep(0.5)

        self._emit_and_log_status(f"Starting position (rotation-robust): ({current_x:.1f}, {current_y:.1f}) μm")

        # Set zero reference point at the upper-left corner of the chip
        self._emit_and_log_status("Setting zero reference point at upper-left corner...")
        try:
            self.stage.set_zero()
            self._emit_and_log_status("✓ Zero reference point set successfully")
            # Update current origin to reflect the new zero position
            self.current_origin_x = 0
            self.current_origin_y = 0

            # Automatically create hybrid corner reference after corner is located
            # This is now MANDATORY - scanning cannot proceed without a valid reference
            reference_success = self._create_automatic_hybrid_corner_reference()
            if not reference_success:
                error_msg = "CRITICAL: Failed to create hybrid corner reference. Scanning cannot proceed."
                self._emit_and_log_status(f"✗ {error_msg}")
                raise RuntimeError(error_msg)

            return 0, 0  # Return zero coordinates since we just set this as the origin
        except Exception as e:
            self._emit_and_log_status(f"⚠ Warning: Failed to set zero reference: {str(e)}")
            # Continue with original coordinates if set_zero fails
            self.current_origin_x = current_x
            self.current_origin_y = current_y
            return current_x, current_y

    def _create_automatic_hybrid_corner_reference(self):
        """
        Automatically create hybrid corner reference using both flake detection and edge keypoints
        after the upper-left corner has been successfully located.

        This method is MANDATORY for scanning to proceed. If it fails, the entire
        scanning process should be aborted.

        Returns:
            bool: True if reference creation succeeded, False if it failed
        """
        self._emit_and_log_status("Creating automatic hybrid corner reference...")

        try:
            # Initialize hybrid alignment system if not already done
            if self.hybrid_alignment_system is None:
                self.hybrid_alignment_system = HybridCornerAlignmentSystem(
                    stage_controller=self.stage,
                    region=self.region,
                    status_callback=self._emit_and_log_status,
                    debug=self.debug
                )
                # Pass debug screenshots folder to alignment system
                if self.debug_screenshots_folder:
                    self.hybrid_alignment_system.debug_screenshots_folder = self.debug_screenshots_folder

            # Create hybrid corner reference using current position as upper-left corner
            self._emit_and_log_status("Creating hybrid corner reference from current position...")

            # Capture current corner image using centralized camera manager with retry
            corner_image = self._get_streaming_image_with_retry(max_retries=3)

            # Save corner image for future re-alignment use with folder name prefix
            if self.scan_folder:
                folder_name = os.path.basename(self.scan_folder.rstrip(os.sep))
                corner_image_filename = f"{folder_name}_corner_image.png"
                corner_image_path = os.path.join(self.scan_folder, corner_image_filename)
            else:
                corner_image_filename = "corner_image.png"
                corner_image_path = corner_image_filename

            cv2.imwrite(corner_image_path, corner_image)
            self._emit_and_log_status(f"✓ Corner reference image saved: {corner_image_path}")

            # Detect flakes in current corner region using detection API
            self._emit_and_log_status("Detecting flakes in corner region...")
            corner_flakes = self._detect_corner_flakes(corner_image)
            self._emit_and_log_status(f"Found {len(corner_flakes)} flakes in corner region")

            # Create metadata for the reference
            metadata = {
                'creation_mode': 'automatic',
                'scan_mode': self.mode,
                'edge_detection_method': type(self.edge_detector).__name__,
                'creation_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'corner_image_file': corner_image_filename,
                'corner_flakes_count': len(corner_flakes)
            }

            reference_result = self.hybrid_alignment_system.create_hybrid_reference(
                corner_flakes=corner_flakes,
                corner_image=corner_image,
                metadata=metadata
            )

            if reference_result.get('success', False):
                self._emit_and_log_status("✓ Automatic hybrid corner reference created successfully")

                # Generate reference filepath using the correct function signature
                if self.scan_folder:
                    reference_filepath = create_hybrid_reference_filename(self.scan_folder)
                else:
                    # If no scan folder, create in current directory
                    reference_filepath = "hybrid_corner_reference.json"

                # Include additional metadata in the reference data
                reference_data = reference_result['reference_data']
                reference_data['edge_detection_method'] = type(self.edge_detector).__name__
                reference_data['creation_mode'] = 'automatic'
                reference_data['scan_mode'] = self.mode
                reference_data['corner_image_file'] = corner_image_filename

                self._emit_and_log_status(f"DEBUG: About to save reference to: {reference_filepath}")
                self._emit_and_log_status(f"DEBUG: Reference data keys: {list(reference_data.keys())}")

                save_success = save_hybrid_reference(reference_data, reference_filepath)
                self._emit_and_log_status(f"DEBUG: save_hybrid_reference returned: {save_success}")

                if save_success:
                    self._emit_and_log_status(f"✓ Reference saved to: {reference_filepath}")
                    self._emit_and_log_status("✓ MANDATORY hybrid corner reference creation completed successfully")
                    self._emit_and_log_status("DEBUG: About to return True from _create_automatic_hybrid_corner_reference")
                    return True
                else:
                    self._emit_and_log_status(f"✗ CRITICAL: Failed to save reference file")
                    self._emit_and_log_status("  → Reference creation succeeded but file save failed")
                    self._emit_and_log_status("  → Check file permissions and disk space")
                    self._emit_and_log_status("DEBUG: About to return False from _create_automatic_hybrid_corner_reference")
                    return False
            else:
                error_msg = reference_result.get('error', 'Unknown error')
                self._emit_and_log_status(f"✗ CRITICAL: Failed to create hybrid corner reference: {error_msg}")
                self._emit_and_log_status("  → Feature detection or corner analysis failed")
                self._emit_and_log_status("  → Check chip positioning and corner visibility")
                return False

        except Exception as e:
            error_msg = f"Exception during automatic reference creation: {str(e)}"
            self._emit_and_log_status(f"✗ CRITICAL: {error_msg}")
            self._emit_and_log_status("  → Unexpected error during reference creation process")
            self._emit_and_log_status("  → Check system resources and hardware connections")
            return False

    def _detect_corner_flakes(self, corner_image):
        """
        Detect flakes in the corner region using the detection API.

        Args:
            corner_image: Corner region image captured during reference creation

        Returns:
            List[Flake]: List of detected flakes in the corner region
        """
        try:
            # Get current stage position for coordinate calculations
            stage_pos = self.stage.get_position()
            current_x_um, current_y_um = stage_pos[1], stage_pos[0]  # Note: stage returns (Y, X)

            # Run detection API on corner image
            result = self.detection_client.infer(corner_image, model_id=DETECTION_MODEL_ID)

            # Process detection results
            import supervision as sv
            svd = sv.Detections.from_inference(result)
            svd = svd[np.isin(svd.class_id, list(SELECTED_CLASSES))]

            # Convert detections to Flake objects
            corner_flakes = []
            h, w, _ = corner_image.shape
            origin_x = w // 2
            origin_y = h // 2

            # Calculate micrometers per pixel
            um_per_pixel_vert = STEP_X_UM / h
            um_per_pixel_horiz = STEP_Y_UM / w

            for i, (bbox, class_id, confidence) in enumerate(zip(svd.xyxy, svd.class_id, svd.confidence)):
                x1, y1, x2, y2 = bbox
                center_x = (x1 + x2) / 2
                center_y = (y1 + y2) / 2

                # Calculate real-world coordinates relative to current stage position
                offset_x_um = (center_x - origin_x) * um_per_pixel_horiz
                offset_y_um = (center_y - origin_y) * um_per_pixel_vert
                real_x_um = current_x_um + offset_x_um
                real_y_um = current_y_um + offset_y_um

                # Get class name
                class_name = list(SELECTED_CLASSES.keys())[list(SELECTED_CLASSES.values()).index(class_id)]

                # Create Flake object
                flake = Flake(
                    id=f"corner_{i}",
                    center_x=center_x,
                    center_y=center_y,
                    real_x_um=real_x_um,
                    real_y_um=real_y_um,
                    shape=f"[{x1:.1f},{y1:.1f},{x2:.1f},{y2:.1f}]",
                    class_name=class_name
                )
                flake.confidence = confidence
                corner_flakes.append(flake)

            return corner_flakes

        except Exception as e:
            self._emit_and_log_status(f"Warning: Failed to detect corner flakes: {str(e)}")
            return []  # Return empty list if detection fails

    def _perform_edge_detection_with_capabilities(self, img):
        """
        Perform edge detection using the selected detector's unified interface.

        All edge detectors (EdgeDetector, BackgroundEdgeDetector, CannyEdgeDetector)
        now have identical detect_edges_with_line_fitting() interfaces, so we can
        use the selected detector consistently without conditional logic.

        Args:
            img: Input image for edge detection

        Returns:
            dict: Edge detection results compatible with validation, or None if failed
        """
        try:
            detector_type = type(self.edge_detector).__name__
            self._emit_and_log_status(f"Using {detector_type} with unified line fitting interface...")

            # All detectors now have the same detect_edges_with_line_fitting() interface
            # Use 'sequential' mode for best accuracy (Hough Transform + RANSAC refinement)
            edge_result = self.edge_detector.detect_edges_with_line_fitting(
                img,
                algorithm_mode='sequential'
            )

            if edge_result is None:
                self._emit_and_log_status(f"✗ {detector_type} returned no results")
                return None

            # Ensure the result has the expected structure
            if not isinstance(edge_result, dict):
                self._emit_and_log_status(f"✗ {detector_type} returned invalid result type: {type(edge_result)}")
                return None

            # Validate required fields
            required_fields = ['edges', 'fitted_lines', 'algorithm_mode']
            missing_fields = [field for field in required_fields if field not in edge_result]
            if missing_fields:
                self._emit_and_log_status(f"✗ {detector_type} result missing fields: {missing_fields}")
                return None

            self._emit_and_log_status(f"✓ {detector_type} edge detection completed successfully")
            return edge_result

        except Exception as e:
            self._emit_and_log_status(f"✗ Error in {detector_type} edge detection: {str(e)}")
            return None

    def _validate_edge_detection_results(self, edge_result):
        """
        Validate the edge detection results to ensure they are suitable
        for hybrid corner reference creation.

        Args:
            edge_result: Result from CannyEdgeDetector.detect_edges_with_line_fitting()

        Returns:
            bool: True if results are valid, False otherwise
        """
        try:
            # Check if we have edge data
            if 'edges' not in edge_result or edge_result['edges'] is None:
                self._emit_and_log_status("✗ Validation failed: No edge data detected")
                return False

            # Check if we have sufficient edge pixels
            edge_pixels = np.sum(edge_result['edges'] > 0)
            if edge_pixels < 100:  # Minimum threshold for meaningful edges
                self._emit_and_log_status(f"✗ Validation failed: Insufficient edge pixels ({edge_pixels} < 100)")
                return False

            # Check if we have line fitting results
            fitted_lines = edge_result.get('fitted_lines', {})
            if not fitted_lines:
                self._emit_and_log_status("✗ Validation failed: No line fitting results")
                return False

            # Check for horizontal and vertical lines
            has_horizontal = fitted_lines.get('horizontal') is not None
            has_vertical = fitted_lines.get('vertical') is not None

            if not (has_horizontal and has_vertical):
                self._emit_and_log_status(f"✗ Validation failed: Missing line orientations (H:{has_horizontal}, V:{has_vertical})")
                return False

            # Validate line confidence if available
            if isinstance(fitted_lines.get('horizontal'), dict):
                h_confidence = fitted_lines['horizontal'].get('confidence', 0)
                if h_confidence < 0.3:  # Minimum confidence threshold
                    self._emit_and_log_status(f"✗ Validation failed: Low horizontal line confidence ({h_confidence:.2f})")
                    return False

            if isinstance(fitted_lines.get('vertical'), dict):
                v_confidence = fitted_lines['vertical'].get('confidence', 0)
                if v_confidence < 0.3:  # Minimum confidence threshold
                    self._emit_and_log_status(f"✗ Validation failed: Low vertical line confidence ({v_confidence:.2f})")
                    return False

            self._emit_and_log_status(f"✓ Edge detection validation passed (edges: {edge_pixels}, lines: H+V)")
            return True

        except Exception as e:
            self._emit_and_log_status(f"✗ Validation failed with exception: {str(e)}")
            return False

    def _find_corner_original_method(self, img, h, w, center_x, center_y):
        """Original corner finding method as fallback"""
        # Find left edge (moving in +Y direction)
        self._emit_and_log_status("Finding left edge...")
        current_y = 0
        while True:
            # Capture image using centralized camera manager
            img = self.camera_manager.get_streaming_image()

            left_check = int(w * 0.2)
            if not self.edge_detector.is_on_chip(img, left_check, center_y):
                self._emit_and_log_status("Found left edge")
                # current_y -= STEP_Y_UM * 0.5
                # self.stage.move_absolute(current_y, 0)
                time.sleep(0.2)
                break

            current_y += STEP_Y_UM * 0.5
            self.stage.move_absolute(current_y, 0)
            time.sleep(0.2)

        # Find top edge (moving in +X direction)
        self._emit_and_log_status("Finding top edge...")
        current_x = 0
        while True:
            # Capture image using centralized camera manager
            img = self.camera_manager.get_streaming_image()

            top_check = int(h * 0.2)
            if not self.edge_detector.is_on_chip(img, center_x, top_check):
                self._emit_and_log_status("Found top edge")
                current_x -= STEP_X_UM * 0.5
                self.stage.move_absolute(current_y, current_x)
                time.sleep(0.2)
                break

            current_x += STEP_X_UM * 0.5
            self.stage.move_absolute(current_y, current_x)
            time.sleep(0.2)

        self._emit_and_log_status(f"Starting position: ({current_x:.1f}, {current_y:.1f}) μm")

        # Set zero reference point at the upper-left corner of the chip
        self._emit_and_log_status("Setting zero reference point at upper-left corner...")
        try:
            self.stage.set_zero()
            self._emit_and_log_status("✓ Zero reference point set successfully")
            # Update current origin to reflect the new zero position
            self.current_origin_x = 0
            self.current_origin_y = 0
            return 0, 0  # Return zero coordinates since we just set this as the origin
        except Exception as e:
            self._emit_and_log_status(f"⚠ Warning: Failed to set zero reference: {str(e)}")
            # Continue with original coordinates if set_zero fails
            self.current_origin_x = current_x
            self.current_origin_y = current_y
            return current_x, current_y

    def run_adaptive_scan(self):
        """
        Enhanced adaptive scanning with rotation robustness.
        Adaptive scanning that follows chip edges with improved rotation handling.
        """
        # Set zero reference point at the very beginning of adaptive scan workflow
        self._emit_and_log_status("Setting initial zero reference point for adaptive scan...")
        try:
            self.stage.set_zero()
            self._emit_and_log_status("✓ Initial zero reference point set successfully")
        except Exception as e:
            self._emit_and_log_status(f"⚠ Warning: Failed to set initial zero reference: {str(e)}")
            # Continue with scan even if set_zero fails

        try:
            start = self.find_upper_left_corner()
            if start is None:
                self._emit_and_log_status("✗ Failed to find upper-left corner - aborting scan")
                return
        except RuntimeError as e:
            # This is raised when hybrid corner reference creation fails
            self._emit_and_log_status(f"✗ SCAN ABORTED: {str(e)}")
            self._emit_and_log_status("✗ Cannot proceed without a valid hybrid corner reference")
            self._emit_and_log_status("✗ Please check:")
            self._emit_and_log_status("  → Chip positioning and visibility")
            self._emit_and_log_status("  → Edge detection parameters")
            self._emit_and_log_status("  → Image quality and lighting")
            return
        except Exception as e:
            self._emit_and_log_status(f"✗ SCAN ABORTED: Unexpected error in corner detection: {str(e)}")
            return

        self._emit_and_log_status("Starting adaptive scan...")

        with open(self.out_csv, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=[
                'step_id', 'step_x', 'step_y', 'pix_origin_x', 'pix_origin_y',
                'center_x', 'center_y', 'points', 'real_x_um', 'real_y_um',
                'class', 'detection_id'
            ])
            writer.writeheader()

            row_num = 0
            total_positions = 0

            while True:
                self._emit_and_log_status(f"\n=== Starting Row {row_num} ===")

                if row_num > 0:
                    current_x = self.current_origin_x - row_num * STEP_X_UM
                    self.stage.move_absolute(self.current_origin_y, current_x)
                    time.sleep(3)

                    # Capture image using centralized camera manager
                    img = self.camera_manager.get_streaming_image()
                    h, w, _ = img.shape

                    # Enhanced rotation-robust edge detection for row start
                    if not self._is_on_chip_rotation_robust(img, w//2, h//2):
                        self._emit_and_log_status(f"Reached bottom edge of chip at row {row_num}")
                        break

                    if not isinstance(self.edge_detector, NoEdgeDetector):
                        current_y = self._find_row_start_rotation_robust(img, current_x, h, w)
                    else:
                        current_y = self.current_origin_y
                else:
                    current_x = self.current_origin_x
                    current_y = self.current_origin_y

                col_num = 0
                self._emit_and_log_status(f"Scanning row {row_num} rightward...")

                # Enhanced row scanning with rotation robustness
                while True:
                    # Capture image using centralized camera manager
                    img = self.camera_manager.get_streaming_image()

                    h, w, _ = img.shape



                    # Enhanced edge detection for column end
                    if not self._is_on_chip_rotation_robust(img, int(w * 0.8), h//2):
                        self._emit_and_log_status(f"Row {row_num}: Reached right edge at column {col_num}")
                        break

                    if isinstance(self.edge_detector, NoEdgeDetector) and col_num >= self.y_steps:
                        self._emit_and_log_status(f"Row {row_num}: Reached column limit ({self.y_steps})")
                        break

                    sid = f"{row_num}-{col_num}"
                    flakes = self.process_position(img, sid, row_num, col_num,
                                                 current_x, current_y, writer)

                    total_positions += 1
                    self.progress.emit(total_positions, total_positions + 10)
                    self._log_progress(total_positions, total_positions + 10)  # Log progress
                    self._emit_and_log_status(f"Position ({row_num},{col_num}): Found {flakes} flakes")

                    col_num += 1
                    current_y -= STEP_Y_UM
                    self.stage.move_absolute(current_y, current_x)
                    time.sleep(1)

                self._emit_and_log_status(f"Row {row_num} complete: {col_num} positions scanned")

                row_num += 1

                if row_num > 100:
                    self._emit_and_log_status("Safety limit reached (100 rows)")
                    break

                if isinstance(self.edge_detector, NoEdgeDetector) and row_num >= self.x_steps:
                    self._emit_and_log_status(f"Reached row limit ({self.x_steps} rows)")
                    break

        self._emit_and_log_status(f"\n=== Scan Complete ===")
        self._emit_and_log_status(f"Total positions: {total_positions}")
        self._emit_and_log_status(f"Total rows: {row_num}")

    def _is_on_chip_rotation_robust(self, img, x, y):
        """
        Enhanced on-chip detection with rotation robustness.

        This method determines if the current stage position is on the chip by checking
        specific regions of the camera view that correspond to the scanning direction.

        Key principle: When scanning rightward, we need to detect when we've moved
        off the RIGHT edge of the chip, not just whether any part of the chip is visible.
        """
        # Get image dimensions
        h, w = img.shape[:2]
        center_x, center_y = w // 2, h // 2

        # Determine what type of edge detection we're doing based on the target position
        if x >= int(w * 0.75):  # Checking right edge area (rightward scanning)
            # For RIGHT EDGE detection during rightward scanning:
            # Focus on the RIGHT side of the image to detect when we've moved off the chip
            right_edge_points = [
                (int(w * 0.8), center_y),       # Right 80% - main detection point
                (int(w * 0.85), center_y),      # Right 85% - further right
                (int(w * 0.9), center_y),       # Right 90% - edge detection
                (int(w * 0.8), int(h * 0.3)),   # Right 80%, upper third
                (int(w * 0.8), int(h * 0.7)),   # Right 80%, lower third
            ]

            right_on_chip = 0
            for check_x, check_y in right_edge_points:
                if self.edge_detector.is_on_chip(img, check_x, check_y, margin=self.edge_margin):
                    right_on_chip += 1

            # CRITICAL: For right edge detection, we need to be STRICT
            # If we've moved off the chip, most right-side points should be off-chip
            # Require at least 3 out of 5 right-edge points to be ON chip to consider position valid
            result = right_on_chip >= 3
            # Debug output for right edge detection
            if not result:
                self._emit_and_log_status(f"Right edge detected: {right_on_chip}/5 right-side points on chip")
            return result

        elif x <= int(w * 0.25):  # Checking left edge area (finding left boundary)
            # For LEFT EDGE detection:
            # Focus on the LEFT side of the image
            left_edge_points = [
                (int(w * 0.2), center_y),       # Left 20% - main detection point
                (int(w * 0.15), center_y),      # Left 15% - further left
                (int(w * 0.1), center_y),       # Left 10% - edge detection
                (int(w * 0.2), int(h * 0.3)),   # Left 20%, upper third
                (int(w * 0.2), int(h * 0.7)),   # Left 20%, lower third
            ]

            left_on_chip = 0
            for check_x, check_y in left_edge_points:
                if self.edge_detector.is_on_chip(img, check_x, check_y, margin=self.edge_margin):
                    left_on_chip += 1

            # For left edge, require at least 3 out of 5 left-edge points to be ON chip
            return left_on_chip >= 3

        else:  # General position checking (center area)
            # For GENERAL position checking (not at edges):
            # Use a focused approach around the center and target position
            general_check_points = [
                (center_x, center_y),           # Center
                (x, y),                         # Target position
                (x, center_y),                  # Target X, center Y
                (center_x, y),                  # Center X, target Y
                (int((x + center_x) / 2), int((y + center_y) / 2)),  # Midpoint
            ]

            general_on_chip = 0
            for check_x, check_y in general_check_points:
                # Ensure coordinates are within image bounds
                check_x = max(0, min(w-1, check_x))
                check_y = max(0, min(h-1, check_y))

                if self.edge_detector.is_on_chip(img, check_x, check_y, margin=self.edge_margin):
                    general_on_chip += 1

            # For general positions, require majority of points to be on chip
            return general_on_chip >= 3

    def _find_row_start_rotation_robust(self, img, current_x, h, w):
        """
        Find row start position with rotation robustness.
        Uses the same approach as the main corner finding but for row starts.
        """
        self._emit_and_log_status(f"Finding left edge for row (rotation-robust)")
        current_y = self.current_origin_y

        # Use multiple check points for better rotation robustness
        left_check_points = [
            (int(w * 0.15), h//2),      # 15% from left, center height
            (int(w * 0.2), h//2),       # 20% from left, center height
            (int(w * 0.25), h//2),      # 25% from left, center height
        ]

        while True:
            # Capture image using centralized camera manager
            img = self.camera_manager.get_streaming_image()
            # Check if any of the left check points are off the chip
            off_chip_count = 0
            for check_x, check_y in left_check_points:
                if not self.edge_detector.is_on_chip(img, check_x, check_y, margin=self.edge_margin):
                    off_chip_count += 1

            # If majority of check points are off chip, we found the left edge
            if off_chip_count >= len(left_check_points) // 2 + 1:
                # Move back onto chip with a small margin
                current_y -= STEP_Y_UM * 0.25
                self.stage.move_absolute(current_y, current_x)
                time.sleep(0.5)
                break

            # Move left (+Y direction)
            current_y += STEP_Y_UM * 0.25
            self.stage.move_absolute(current_y, current_x)
            time.sleep(0.5)

        return current_y

    def convert_csv_to_flakes(self) -> List[Flake]:
        """Convert CSV scan results to Flake objects for hybrid corner alignment"""
        flakes = []

        try:
            import csv
            import os

            # Check if CSV file exists
            if not os.path.exists(self.out_csv):
                self._emit_and_log_status(f"Error: CSV file not found: {self.out_csv}")
                return flakes

            self._emit_and_log_status(f"Converting CSV to flakes from: {self.out_csv}")

            with open(self.out_csv, 'r') as f:
                reader = csv.DictReader(f)
                row_count = 0
                for row in reader:
                    row_count += 1
                    try:
                        # Parse polygon points
                        import ast
                        points = ast.literal_eval(row['points'])

                        # Create Flake object
                        flake = Flake(
                            id=f"{row['step_id']}_{row['detection_id']}",
                            center_x=float(row['center_x']),
                            center_y=float(row['center_y']),
                            real_x_um=float(row['real_x_um']),
                            real_y_um=float(row['real_y_um']),
                            shape=points,
                            class_name=row['class']
                        )
                        flakes.append(flake)
                    except Exception as row_error:
                        self._emit_and_log_status(f"Error processing row {row_count}: {str(row_error)}")
                        continue

            self._emit_and_log_status(f"Successfully converted {len(flakes)} flakes from {row_count} CSV rows")

        except Exception as e:
            self._emit_and_log_status(f"Error converting CSV to flakes: {str(e)}")
            import traceback
            self._emit_and_log_status(f"Traceback: {traceback.format_exc()}")

        return flakes

    def create_composite_annotated_image(self) -> Dict:
        """Create a composite annotated image by stitching individual annotated images"""
        try:
            import os
            import glob
            import numpy as np

            self._emit_and_log_status("Scanning for annotated images...")

            # Find all annotated images
            annot_files = glob.glob("annot_*.png")
            if not annot_files:
                return {'success': False, 'error': 'No annotated images found'}

            self._emit_and_log_status(f"Found {len(annot_files)} annotated images")

            # Parse step information from filenames and CSV data
            step_info = {}

            # Read CSV to get coordinate information
            if not os.path.exists(self.out_csv):
                return {'success': False, 'error': 'CSV file not found for coordinate data'}

            import csv
            with open(self.out_csv, 'r') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    step_id = row['step_id']
                    if step_id not in step_info:
                        step_info[step_id] = {
                            'step_x': int(row['step_x']),
                            'step_y': int(row['step_y']),
                            'real_x_um': float(row['real_x_um']),
                            'real_y_um': float(row['real_y_um']),
                            'has_image': f"annot_{step_id}.png" in annot_files
                        }

            # Filter to only steps that have images
            valid_steps = {k: v for k, v in step_info.items() if v['has_image']}

            if not valid_steps:
                return {'success': False, 'error': 'No valid step images found'}

            self._emit_and_log_status(f"Processing {len(valid_steps)} valid step positions")

            # Calculate grid dimensions and bounds
            step_x_coords = [info['step_x'] for info in valid_steps.values()]
            step_y_coords = [info['step_y'] for info in valid_steps.values()]

            min_step_x, max_step_x = min(step_x_coords), max(step_x_coords)
            min_step_y, max_step_y = min(step_y_coords), max(step_y_coords)

            grid_width = max_step_y - min_step_y + 1
            grid_height = max_step_x - min_step_x + 1

            self._emit_and_log_status(f"Grid dimensions: {grid_width} x {grid_height}")

            # Load a sample image to get dimensions
            sample_img = cv2.imread(f"annot_{list(valid_steps.keys())[0]}.png")
            if sample_img is None:
                return {'success': False, 'error': 'Could not load sample image'}

            img_height, img_width = sample_img.shape[:2]

            # Create composite image
            composite_width = grid_width * img_width
            composite_height = grid_height * img_height

            self._emit_and_log_status(f"Creating composite image: {composite_width} x {composite_height} pixels")

            # Initialize composite with black background
            composite = np.zeros((composite_height, composite_width, 3), dtype=np.uint8)

            # Stitch images
            stitched_count = 0
            for step_id, info in valid_steps.items():
                img_path = f"annot_{step_id}.png"
                img = cv2.imread(img_path)

                if img is not None:
                    # Calculate position in composite
                    grid_x = info['step_y'] - min_step_y
                    grid_y = info['step_x'] - min_step_x

                    start_x = grid_x * img_width
                    start_y = grid_y * img_height
                    end_x = start_x + img_width
                    end_y = start_y + img_height

                    # Place image in composite
                    composite[start_y:end_y, start_x:end_x] = img
                    stitched_count += 1

            # Save composite image
            base_name = os.path.splitext(self.out_csv)[0]
            composite_filename = f"{base_name}_composite_annotated.png"

            success = cv2.imwrite(composite_filename, composite)

            if success:
                self._emit_and_log_status(f"Successfully stitched {stitched_count} images")
                return {
                    'success': True,
                    'filename': composite_filename,
                    'stitched_count': stitched_count,
                    'total_images': len(annot_files),
                    'composite_size': (composite_width, composite_height)
                }
            else:
                return {'success': False, 'error': 'Failed to save composite image'}

        except Exception as e:
            import traceback
            error_msg = f"Error creating composite image: {str(e)}"
            self._emit_and_log_status(f"DEBUG: {error_msg}")
            self._emit_and_log_status(f"DEBUG: Traceback: {traceback.format_exc()}")
            return {'success': False, 'error': error_msg}

    # NOTE: Hybrid corner reference creation has been moved to scan initialization
    # Reference creation now occurs immediately when upper-left corner is detected
    # This ensures mandatory blocking behavior as required by the scanning workflow

    def perform_quick_realignment(self, scan_folder_path: str = None) -> Dict:
        """
        Perform complete quick re-alignment workflow using hybrid corner referencing.

        This method implements the full re-alignment workflow:
        1. Load original scan data (CSV, corner image, reference JSON)
        2. Find current upper-left corner and capture current corner image
        3. Perform feature detection and matching using geometric hashing
        4. Calculate transformation matrix using actual HybridCornerAlignmentSystem methods
        5. Apply transformation to original flake coordinates
        6. Return transformed coordinates for flake selector integration

        Args:
            scan_folder_path: Path to original scan folder (if None, will prompt user)

        Returns:
            Dict with success status, transformation data, and transformed flake coordinates
        """
        if not self.enable_hybrid_alignment:
            return {'success': False, 'error': 'Hybrid corner alignment not enabled'}

        try:
            self._emit_and_log_status("=== STARTING COMPLETE QUICK RE-ALIGNMENT WORKFLOW ===")

            # Step 1: Scan folder selection and validation
            if not scan_folder_path:
                self._emit_and_log_status("ERROR: Scan folder path not provided")
                return {'success': False, 'error': 'Scan folder path required for re-alignment'}

            if not os.path.exists(scan_folder_path):
                return {'success': False, 'error': f'Scan folder does not exist: {scan_folder_path}'}

            self._emit_and_log_status(f"Using scan folder: {scan_folder_path}")

            # Step 2: Load original data files
            self._emit_and_log_status("Loading original scan data...")

            # Extract folder name for file pattern matching
            folder_name = os.path.basename(scan_folder_path.rstrip(os.sep))

            # Find original CSV file using new naming convention
            expected_csv_name = f"{folder_name}_flake_data.csv"
            csv_files = [f for f in os.listdir(scan_folder_path) if f.endswith('_flake_data.csv')]
            if not csv_files:
                return {'success': False, 'error': f'No flake data CSV file found in scan folder (expected: {expected_csv_name})'}

            original_csv_path = os.path.join(scan_folder_path, csv_files[0])
            self._emit_and_log_status(f"Found original CSV: {original_csv_path}")

            # Find hybrid corner reference JSON file using new naming convention
            expected_json_name = f"{folder_name}_hybrid_reference.json"
            json_files = [f for f in os.listdir(scan_folder_path) if f.endswith('_hybrid_reference.json')]
            if not json_files:
                return {'success': False, 'error': f'No hybrid corner reference JSON file found in scan folder (expected: {expected_json_name})'}

            reference_json_path = os.path.join(scan_folder_path, json_files[0])
            self._emit_and_log_status(f"Found reference JSON: {reference_json_path}")

            # Find original corner reference image using new naming convention
            expected_image_name = f"{folder_name}_corner_image.png"
            corner_images = [f for f in os.listdir(scan_folder_path) if f.endswith('_corner_image.png')]
            if not corner_images:
                return {'success': False, 'error': f'No corner reference image found in scan folder (expected: {expected_image_name})'}

            original_corner_image_path = os.path.join(scan_folder_path, corner_images[0])
            self._emit_and_log_status(f"Found corner image: {original_corner_image_path}")

            # Step 3: Load original flake database from CSV
            self._emit_and_log_status("Loading original flake database...")
            original_flakes = self._load_flakes_from_csv(original_csv_path)
            if not original_flakes:
                return {'success': False, 'error': 'Failed to load flakes from original CSV'}

            self._emit_and_log_status(f"Loaded {len(original_flakes)} original flakes")

            # Step 4: Load original corner reference image
            original_corner_image = cv2.imread(original_corner_image_path)
            if original_corner_image is None:
                return {'success': False, 'error': f'Failed to load corner image: {original_corner_image_path}'}

            # Step 5: Load hybrid corner reference data
            reference_data = load_hybrid_reference(reference_json_path)
            if reference_data is None:
                return {'success': False, 'error': f'Failed to load reference data: {reference_json_path}'}

            feature_counts = reference_data.get('feature_counts', {})
            self._emit_and_log_status(f"Loaded reference with {feature_counts.get('total', 0)} features")

            return self._continue_realignment_workflow(
                original_flakes, original_corner_image, reference_data, scan_folder_path
            )

        except Exception as e:
            import traceback
            error_msg = f"Exception in quick re-alignment: {str(e)}"
            self._emit_and_log_status(f"ERROR: {error_msg}")
            self._emit_and_log_status(f"Traceback: {traceback.format_exc()}")
            return {'success': False, 'error': error_msg}

    def _continue_realignment_workflow(self, original_flakes, original_corner_image, reference_data, scan_folder_path):
        """Continue the re-alignment workflow with current corner capture and transformation calculation."""
        # Note: original_corner_image parameter kept for potential future debugging/comparison features
        try:
            # Step 6: Find current upper-left corner position
            self._emit_and_log_status("Finding current upper-left corner position...")

            # Set zero reference point at the beginning
            self._emit_and_log_status("Setting zero reference point for re-alignment...")
            try:
                self.stage.set_zero()
                self._emit_and_log_status("✓ Zero reference point set successfully")
            except Exception as e:
                self._emit_and_log_status(f"⚠ Warning: Failed to set zero reference: {str(e)}")

            # Find upper-left corner using existing edge detection
            corner_x, corner_y = self.find_upper_left_corner()
            self._emit_and_log_status(f"Current upper-left corner found at: ({corner_x:.2f}, {corner_y:.2f}) μm")

            # Step 7: Capture current corner screenshot using centralized camera manager
            self._emit_and_log_status("Capturing current corner screenshot...")
            current_corner_image = self.camera_manager.get_streaming_image()

            # Save current corner image with timestamped filename
            timestamp = int(time.time())
            original_scan_name = os.path.splitext(os.path.basename(scan_folder_path))[0]
            current_corner_filename = f"{original_scan_name}_realigned_{timestamp}.png"
            current_corner_path = os.path.join(scan_folder_path, current_corner_filename)
            cv2.imwrite(current_corner_path, current_corner_image)
            self._emit_and_log_status(f"Current corner image saved: {current_corner_path}")

            # Step 8: Initialize hybrid alignment system if needed
            if self.hybrid_alignment_system is None:
                self.hybrid_alignment_system = HybridCornerAlignmentSystem(
                    stage_controller=self.stage,
                    region=self.region,
                    status_callback=self._emit_and_log_status,
                    debug=self.debug
                )
                if self.debug_screenshots_folder:
                    self.hybrid_alignment_system.debug_screenshots_folder = self.debug_screenshots_folder

            # Step 9: Perform hybrid corner re-alignment using actual HybridCornerAlignmentSystem method
            self._emit_and_log_status("Performing feature detection and matching...")
            realignment_result = self.hybrid_alignment_system.perform_hybrid_realignment(
                reference_data=reference_data,
                current_flakes=[]  # Let the system detect features from current corner
            )

            if not realignment_result['success']:
                return {
                    'success': False,
                    'error': f"Re-alignment failed: {realignment_result['error']}"
                }

            # Step 10: Extract transformation matrix
            transformation = realignment_result['transformation']
            self._emit_and_log_status(f"✓ Re-alignment successful!")
            self._emit_and_log_status(f"Translation: ({transformation['translation'][0]:.2f}, {transformation['translation'][1]:.2f}) μm")
            self._emit_and_log_status(f"Rotation: {transformation['rotation_degrees']:.2f}°")
            self._emit_and_log_status(f"Confidence: {realignment_result['confidence']:.3f}")
            self._emit_and_log_status(f"Matched features: {realignment_result.get('matched_features', 0)}")

            return self._apply_transformation_and_finalize(
                original_flakes, transformation, realignment_result, scan_folder_path
            )

        except Exception as e:
            import traceback
            error_msg = f"Exception in re-alignment workflow continuation: {str(e)}"
            self._emit_and_log_status(f"ERROR: {error_msg}")
            self._emit_and_log_status(f"Traceback: {traceback.format_exc()}")
            return {'success': False, 'error': error_msg}

    def _apply_transformation_and_finalize(self, original_flakes, transformation, realignment_result, scan_folder_path):
        """Apply transformation matrix to original flake coordinates and prepare for flake selector."""
        try:
            # Step 11: Apply transformation matrix to original flake coordinates
            self._emit_and_log_status("Applying transformation to original flake coordinates...")

            # Extract transformation matrix components
            translation = transformation['translation']
            rotation_matrix = transformation['rotation_matrix']

            transformed_flakes = []
            for flake in original_flakes:
                # Apply rotation and translation transformation
                original_point = np.array([flake.real_x_um, flake.real_y_um])

                # Apply rotation matrix
                rotated_point = rotation_matrix @ original_point

                # Apply translation
                transformed_point = rotated_point + np.array(translation)

                # Create transformed flake
                transformed_flake = Flake(
                    id=flake.id,
                    center_x=flake.center_x,  # Keep original pixel coordinates
                    center_y=flake.center_y,
                    real_x_um=transformed_point[0],  # Use transformed real coordinates
                    real_y_um=transformed_point[1],
                    shape=flake.shape,
                    class_name=flake.class_name
                )
                transformed_flakes.append(transformed_flake)

            self._emit_and_log_status(f"✓ Transformed {len(transformed_flakes)} flake coordinates")

            # Step 12: Prepare result for flake selector integration
            result = {
                'success': True,
                'transformation': transformation,
                'original_flakes': original_flakes,
                'transformed_flakes': transformed_flakes,
                'confidence': realignment_result['confidence'],
                'matched_features': realignment_result.get('matched_features', 0),
                'scan_folder': scan_folder_path,
                'realignment_timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }

            self._emit_and_log_status("=== QUICK RE-ALIGNMENT COMPLETED SUCCESSFULLY ===")
            self._emit_and_log_status(f"✓ {len(transformed_flakes)} flakes ready for navigation")
            self._emit_and_log_status(f"✓ Transformation confidence: {realignment_result['confidence']:.3f}")
            self._emit_and_log_status(f"✓ Ready for flake selector integration")

            return result

        except Exception as e:
            import traceback
            error_msg = f"Exception in transformation application: {str(e)}"
            self._emit_and_log_status(f"ERROR: {error_msg}")
            self._emit_and_log_status(f"Traceback: {traceback.format_exc()}")
            return {'success': False, 'error': error_msg}

    def _load_flakes_from_csv(self, csv_path: str):
        """Load flakes from CSV file and convert to Flake objects."""
        try:
            import csv
            flakes = []

            with open(csv_path, 'r') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    try:
                        flake = Flake(
                            id=int(row.get('id', 0)),
                            center_x=float(row.get('center_x', 0)),
                            center_y=float(row.get('center_y', 0)),
                            real_x_um=float(row.get('real_x_um', 0)),
                            real_y_um=float(row.get('real_y_um', 0)),
                            shape=row.get('shape', ''),
                            class_name=row.get('class_name', '')
                        )
                        flakes.append(flake)
                    except (ValueError, KeyError) as e:
                        self._emit_and_log_status(f"Warning: Skipping invalid flake row: {str(e)}")
                        continue

            return flakes

        except Exception as e:
            self._emit_and_log_status(f"Error loading flakes from CSV: {str(e)}")
            return []

    def load_csv_and_apply_transformation(self, csv_file: str, transformation: Dict) -> Dict:
        """Load CSV file and apply transformation matrix to flake coordinates using hybrid corner utilities"""
        try:
            import csv
            import ast

            self._emit_and_log_status(f"Loading CSV file: {csv_file}")

            transformed_flakes = []

            with open(csv_file, 'r') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    try:
                        # Get original coordinates
                        orig_x = float(row['real_x_um'])
                        orig_y = float(row['real_y_um'])

                        # Apply transformation manually (compatible with hybrid corner format)
                        translation = transformation['translation']
                        rotation_rad = transformation['rotation_radians']

                        import numpy as np
                        cos_r = np.cos(rotation_rad)
                        sin_r = np.sin(rotation_rad)

                        # Apply rotation
                        rotated_x = orig_x * cos_r - orig_y * sin_r
                        rotated_y = orig_x * sin_r + orig_y * cos_r

                        # Apply translation
                        transformed_x = rotated_x + translation[0]
                        transformed_y = rotated_y + translation[1]

                        # Create transformed flake data
                        flake_data = {
                            'id': f"{row['step_id']}_{row['detection_id']}",
                            'step_id': row['step_id'],
                            'detection_id': row['detection_id'],
                            'center_x': float(row['center_x']),
                            'center_y': float(row['center_y']),
                            'real_x_um': orig_x,
                            'real_y_um': orig_y,
                            'real_x_um_transformed': transformed_x,
                            'real_y_um_transformed': transformed_y,
                            'shape': ast.literal_eval(row['points']),
                            'class_name': row['class'],
                            'pix_origin_x': float(row['pix_origin_x']),
                            'pix_origin_y': float(row['pix_origin_y'])
                        }
                        transformed_flakes.append(flake_data)

                    except Exception as row_error:
                        self._emit_and_log_status(f"Error processing row: {str(row_error)}")
                        continue

            self._emit_and_log_status(f"Successfully transformed {len(transformed_flakes)} flakes")

            return {
                'success': True,
                'transformed_flakes': transformed_flakes,
                'original_csv': csv_file,
                'transformation': transformation
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _save_aligned_flakes(self, transformed_flakes: List[Dict], output_file: str):
        """Save transformed flake coordinates to CSV"""
        try:
            import csv
            with open(output_file, 'w', newline='') as f:
                fieldnames = [
                    'step_id', 'detection_id', 'center_x', 'center_y',
                    'real_x_um', 'real_y_um', 'real_x_um_transformed', 'real_y_um_transformed',
                    'points', 'class', 'pix_origin_x', 'pix_origin_y'
                ]
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()

                for flake in transformed_flakes:
                    writer.writerow({
                        'step_id': flake.get('step_id', ''),
                        'detection_id': flake.get('detection_id', ''),
                        'center_x': flake['center_x'],
                        'center_y': flake['center_y'],
                        'real_x_um': flake['real_x_um'],
                        'real_y_um': flake['real_y_um'],
                        'real_x_um_transformed': flake['real_x_um_transformed'],
                        'real_y_um_transformed': flake['real_y_um_transformed'],
                        'points': str(flake['shape']),
                        'class': flake['class_name'],
                        'pix_origin_x': flake.get('pix_origin_x', 0),
                        'pix_origin_y': flake.get('pix_origin_y', 0)
                    })
        except Exception as e:
            self._emit_and_log_status(f"Error saving aligned flakes: {str(e)}")

    def run(self):
        """Main run method with enhanced error handling and hybrid corner alignment"""
        try:
            # Ensure camera streaming is active before starting any operations
            if not self._ensure_streaming_active():
                self._emit_and_log_status("✗ Failed to start camera streaming - aborting scan")
                self.finished.emit("")
                return

            # Check if this is a re-alignment operation
            if self.enable_hybrid_alignment and self.reference_file:
                # Perform quick re-alignment instead of full scan
                self._emit_and_log_status("=== HYBRID CORNER RE-ALIGNMENT MODE ===")
                result = self.perform_quick_realignment()

                # Store result for UI access
                self.last_realignment_result = result

                if result['success']:
                    self._emit_and_log_status("✓ Quick hybrid corner re-alignment completed successfully!")
                    self._log_workflow_completion("HYBRID-REALIGNMENT", True, "Transformation matrix calculated")
                    self.finished.emit("realignment_success")  # Signal success to UI
                else:
                    self._emit_and_log_status(f"✗ Hybrid corner re-alignment failed: {result['error']}")
                    self._log_workflow_completion("HYBRID-REALIGNMENT", False, result['error'])
                    self.finished.emit("")

                self.stage.close()
                return

            # Perform normal scanning
            if self.mode == 'grid':
                try:
                    self.run_grid_scan()
                    self._log_workflow_completion("GRID SCANNING", True, f"Output: {self.out_csv}")
                except RuntimeError as e:
                    # This is raised when hybrid corner reference creation fails
                    self._emit_and_log_status(f"✗ GRID SCAN ABORTED: {str(e)}")
                    self._emit_and_log_status("✗ Cannot proceed without a valid hybrid corner reference")
                    self._log_workflow_completion("GRID SCANNING", False, str(e))
                    self.finished.emit("")
                    return
                except Exception as e:
                    self._emit_and_log_status(f"✗ GRID SCAN ABORTED: Unexpected error: {str(e)}")
                    self._log_workflow_completion("GRID SCANNING", False, str(e))
                    self.finished.emit("")
                    return
            else:
                try:
                    self.run_adaptive_scan()
                    self._log_workflow_completion("ADAPTIVE SCANNING", True, f"Output: {self.out_csv}")
                except RuntimeError as e:
                    # This is raised when hybrid corner reference creation fails
                    self._emit_and_log_status(f"✗ ADAPTIVE SCAN ABORTED: {str(e)}")
                    self._emit_and_log_status("✗ Cannot proceed without a valid hybrid corner reference")
                    self._log_workflow_completion("ADAPTIVE SCANNING", False, str(e))
                    self.finished.emit("")
                    return
                except Exception as e:
                    self._emit_and_log_status(f"✗ ADAPTIVE SCAN ABORTED: Unexpected error: {str(e)}")
                    self._log_workflow_completion("ADAPTIVE SCANNING", False, str(e))
                    self.finished.emit("")
                    return

            # Note: Hybrid corner reference is now created automatically at the beginning
            # of the scan (in find_upper_left_corner) rather than at the end.
            # This ensures every scan has a valid reference file from the start.

            # Create composite annotated image after scan completion
            if self.mode in ['grid', 'adaptive']:
                self._emit_and_log_status("\n=== CREATING COMPOSITE ANNOTATED IMAGE ===")
                composite_result = self.create_composite_annotated_image()
                if composite_result['success']:
                    self._emit_and_log_status(f"✓ Composite image created: {composite_result['filename']}")
                else:
                    self._emit_and_log_status(f"⚠ Warning: Could not create composite image: {composite_result['error']}")

            self.stage.close()
            # Camera disconnection handled by CentralizedCameraManager
            self.finished.emit(self.out_csv)

        except Exception as e:
            self._emit_and_log_status(f"Error: {str(e)}")
            self._log_workflow_completion("SCANNING", False, str(e))
            try:
                self.stage.close()
            except:
                pass
            # Camera disconnection handled by CentralizedCameraManager
            self.finished.emit("")