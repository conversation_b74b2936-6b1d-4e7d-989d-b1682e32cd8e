#!/usr/bin/env python3
"""
Test Live Image Buffer Fix

Test script to verify that the get_live_image() buffer issues have been resolved
and that get_streaming_image() now returns valid image data consistently.
"""

import sys
import time
import threading
from PyQt6.QtWidgets import <PERSON>Application, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt6.QtCore import QTimer, QThread, pyqtSignal

from camera_manager import CentralizedCameraManager, VideoStreamingManager


class LiveImageBufferTestWorker(QThread):
    """Worker thread to test live image buffer functionality."""
    
    statusUpdate = pyqtSignal(str)
    
    def __init__(self, test_type="buffer_warmup"):
        super().__init__()
        self.test_type = test_type
        self.stopped = False
        
    def run(self):
        """Run live image buffer test."""
        try:
            if self.test_type == "buffer_warmup":
                self.test_buffer_warmup()
            elif self.test_type == "polling_consistency":
                self.test_polling_consistency()
            elif self.test_type == "streaming_stability":
                self.test_streaming_stability()
        except Exception as e:
            self.statusUpdate.emit(f"✗ {self.test_type} test failed: {e}")
            import traceback
            traceback.print_exc()
    
    def test_buffer_warmup(self):
        """Test buffer warm-up and initialization."""
        self.statusUpdate.emit("Testing live image buffer warm-up...")
        
        camera_manager = CentralizedCameraManager(camera_index=0)
        
        try:
            # Start streaming with enhanced initialization
            self.statusUpdate.emit("Starting streaming with buffer warm-up...")
            start_time = time.time()
            success = camera_manager.start_streaming()
            init_time = time.time() - start_time
            
            if not success:
                self.statusUpdate.emit("✗ Failed to start streaming")
                return
            
            self.statusUpdate.emit(f"✓ Streaming started in {init_time:.2f}s")
            
            # Test immediate image capture after warm-up
            for i in range(5):
                if self.stopped:
                    break
                    
                try:
                    self.statusUpdate.emit(f"Testing immediate capture {i+1}/5...")
                    start_time = time.time()
                    img = camera_manager.get_streaming_image()
                    capture_time = time.time() - start_time
                    
                    if img is not None:
                        self.statusUpdate.emit(f"✓ Immediate capture {i+1}/5 successful in {capture_time:.3f}s: {img.shape}")
                    else:
                        self.statusUpdate.emit(f"⚠ Immediate capture {i+1}/5 returned None")
                    
                    time.sleep(0.2)
                except Exception as e:
                    self.statusUpdate.emit(f"✗ Immediate capture {i+1}/5 failed: {e}")
            
            camera_manager.stop_streaming()
            self.statusUpdate.emit("✓ Buffer warm-up test completed")
            
        except Exception as e:
            self.statusUpdate.emit(f"✗ Buffer warm-up test failed: {e}")
    
    def test_polling_consistency(self):
        """Test polling consistency and retry logic."""
        self.statusUpdate.emit("Testing polling consistency and retry logic...")
        
        camera_manager = CentralizedCameraManager(camera_index=0)
        
        try:
            # Start streaming
            success = camera_manager.start_streaming()
            if not success:
                self.statusUpdate.emit("✗ Failed to start streaming for polling test")
                return
            
            self.statusUpdate.emit("✓ Streaming started for polling test")
            
            # Test polling with different parameters
            test_configs = [
                {"max_poll_attempts": 1, "poll_delay": 0.01},
                {"max_poll_attempts": 3, "poll_delay": 0.02},
                {"max_poll_attempts": 5, "poll_delay": 0.05},
            ]
            
            for config in test_configs:
                if self.stopped:
                    break
                    
                attempts = config["max_poll_attempts"]
                delay = config["poll_delay"]
                
                self.statusUpdate.emit(f"Testing polling: {attempts} attempts, {delay}s delay...")
                
                success_count = 0
                total_tests = 10
                
                for i in range(total_tests):
                    try:
                        img = camera_manager.get_streaming_image(
                            max_poll_attempts=attempts,
                            poll_delay=delay
                        )
                        if img is not None:
                            success_count += 1
                        time.sleep(0.1)
                    except Exception as e:
                        self.statusUpdate.emit(f"⚠ Polling error: {e}")
                
                success_rate = (success_count / total_tests) * 100
                self.statusUpdate.emit(f"✓ Polling config {attempts}/{delay}: {success_count}/{total_tests} ({success_rate:.1f}% success)")
            
            camera_manager.stop_streaming()
            self.statusUpdate.emit("✓ Polling consistency test completed")
            
        except Exception as e:
            self.statusUpdate.emit(f"✗ Polling consistency test failed: {e}")
    
    def test_streaming_stability(self):
        """Test streaming stability over extended period."""
        self.statusUpdate.emit("Testing streaming stability over extended period...")
        
        camera_manager = CentralizedCameraManager(camera_index=0)
        
        try:
            # Start streaming
            success = camera_manager.start_streaming()
            if not success:
                self.statusUpdate.emit("✗ Failed to start streaming for stability test")
                return
            
            self.statusUpdate.emit("✓ Streaming started for stability test")
            
            # Monitor streaming over 60 seconds
            start_time = time.time()
            capture_count = 0
            success_count = 0
            none_count = 0
            error_count = 0
            
            while time.time() - start_time < 60.0 and not self.stopped:
                try:
                    img = camera_manager.get_streaming_image(max_poll_attempts=3)
                    capture_count += 1
                    
                    if img is not None:
                        success_count += 1
                        if capture_count % 20 == 0:  # Report every 20th capture
                            elapsed = time.time() - start_time
                            self.statusUpdate.emit(f"Stability test: {success_count}/{capture_count} successful after {elapsed:.1f}s")
                    else:
                        none_count += 1
                    
                    time.sleep(0.5)  # 2 FPS capture rate
                    
                except Exception as e:
                    error_count += 1
                    if error_count <= 3:  # Only report first few errors
                        self.statusUpdate.emit(f"⚠ Stability test error: {e}")
            
            # Final statistics
            success_rate = (success_count / capture_count * 100) if capture_count > 0 else 0
            none_rate = (none_count / capture_count * 100) if capture_count > 0 else 0
            error_rate = (error_count / capture_count * 100) if capture_count > 0 else 0
            
            self.statusUpdate.emit(f"✓ Stability test completed:")
            self.statusUpdate.emit(f"  Total captures: {capture_count}")
            self.statusUpdate.emit(f"  Successful: {success_count} ({success_rate:.1f}%)")
            self.statusUpdate.emit(f"  None returns: {none_count} ({none_rate:.1f}%)")
            self.statusUpdate.emit(f"  Errors: {error_count} ({error_rate:.1f}%)")
            
            camera_manager.stop_streaming()
            
        except Exception as e:
            self.statusUpdate.emit(f"✗ Streaming stability test failed: {e}")
    
    def stop(self):
        """Stop the test worker."""
        self.stopped = True


class LiveImageBufferTestWindow(QMainWindow):
    """Test window for live image buffer fixes."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Live Image Buffer Fix Test")
        self.setGeometry(100, 100, 900, 700)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Status label
        self.status_label = QLabel("Ready to test live image buffer fixes...")
        layout.addWidget(self.status_label)
        
        # Test buttons
        warmup_button = QPushButton("Test Buffer Warm-up")
        warmup_button.clicked.connect(lambda: self.run_test("buffer_warmup"))
        layout.addWidget(warmup_button)
        
        polling_button = QPushButton("Test Polling Consistency")
        polling_button.clicked.connect(lambda: self.run_test("polling_consistency"))
        layout.addWidget(polling_button)
        
        stability_button = QPushButton("Test Streaming Stability")
        stability_button.clicked.connect(lambda: self.run_test("streaming_stability"))
        layout.addWidget(stability_button)
        
        # Output text area
        self.output_text = QTextEdit()
        self.output_text.setReadOnly(True)
        layout.addWidget(self.output_text)
        
        # Test worker
        self.test_worker = None
        
    def run_test(self, test_type):
        """Run a specific live image buffer test."""
        if self.test_worker and self.test_worker.isRunning():
            self.test_worker.stop()
            self.test_worker.wait()
        
        self.output_text.clear()
        self.status_label.setText(f"Running {test_type} test...")
        
        self.test_worker = LiveImageBufferTestWorker(test_type)
        self.test_worker.statusUpdate.connect(self.update_output)
        self.test_worker.finished.connect(lambda: self.status_label.setText("Test completed"))
        self.test_worker.start()
    
    def update_output(self, message):
        """Update the output text area."""
        self.output_text.append(message)
        
    def closeEvent(self, event):
        """Handle window close event."""
        if self.test_worker and self.test_worker.isRunning():
            self.test_worker.stop()
            self.test_worker.wait()
        event.accept()


def main():
    """Main function to run the live image buffer test."""
    app = QApplication(sys.argv)
    
    # Create and show test window
    window = LiveImageBufferTestWindow()
    window.show()
    
    print("Live Image Buffer Fix Test")
    print("=" * 40)
    print("This test verifies:")
    print("1. Buffer warm-up and initialization after start_live()")
    print("2. Polling consistency with retry logic")
    print("3. Streaming stability over extended periods")
    print("4. Resolution of get_live_image() returning None")
    print()
    print("Key fixes implemented:")
    print("- Buffer warm-up delay after start_live()")
    print("- Enhanced polling with retry logic")
    print("- Proper frame transfer initialization")
    print("- Comprehensive error handling and diagnostics")
    print()
    print("Click the test buttons to verify buffer fixes.")
    print()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
