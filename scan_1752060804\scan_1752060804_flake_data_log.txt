================================================================================
SCANNING OPERATION LOG - 2025-07-09 19:33:26
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752060804\scan_1752060804_flake_data.csv
Grid Steps: 0 x 0
Edge Method: general
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752060804
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752060804\debug_screenshots
================================================================================

[2025-07-09 19:33:26.762] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-09 19:33:26.772] [INFO] [SYSTEM] Using custom scan folder: scan_1752060804
[2025-07-09 19:33:26.789] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-09 19:33:26.909] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-09 19:33:26.929] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-09 19:33:26.939] [INFO] [STATUS] ✗ SCAN ABORTED: Unexpected error in corner detection: 'NoneType' object has no attribute 'shape'
[2025-07-09 19:33:26.949] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1752060804\scan_1752060804_flake_data.csv
[2025-07-09 19:33:26.958] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-09 19:33:26.970] [INFO] [STATUS] Scanning for annotated images...
[2025-07-09 19:33:26.983] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
