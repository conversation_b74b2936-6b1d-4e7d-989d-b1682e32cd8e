================================================================================
SCANNING OPERATION LOG - 2025-07-09 20:19:20
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752063557\scan_1752063557_flake_data.csv
Grid Steps: 0 x 0
Edge Method: background
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752063557
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752063557\debug_screenshots
================================================================================

[2025-07-09 20:19:20.442] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-09 20:19:20.452] [INFO] [SYSTEM] Using custom scan folder: scan_1752063557
[2025-07-09 20:19:20.469] [INFO] [SUCCESS] ✓ Camera streaming already active
[2025-07-09 20:19:20.491] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-09 20:19:20.595] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-09 20:19:20.606] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-09 20:19:21.104] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-07-09 20:19:22.525] [INFO] [POSITION] Position feedback: (4.44, 0.00) μm
[2025-07-09 20:19:24.456] [INFO] [POSITION] Position feedback: (176.92, 0.00) μm
[2025-07-09 20:19:26.265] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-07-09 20:19:26.405] [INFO] [POSITION] Position feedback: (345.59, 0.00) μm
[2025-07-09 20:19:26.616] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-07-09 20:19:27.879] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-07-09 20:19:28.025] [INFO] [POSITION] Position feedback: (345.59, 0.00) μm
[2025-07-09 20:19:28.536] [INFO] [WORKFLOW] Starting position (rotation-robust): (0.0, 345.6) μm
[2025-07-09 20:19:28.546] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-07-09 20:19:28.685] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-07-09 20:19:28.695] [INFO] [STATUS] Creating automatic hybrid corner reference...
[2025-07-09 20:19:28.707] [INFO] [STATUS] Creating hybrid corner reference from current position...
[2025-07-09 20:19:28.845] [INFO] [SUCCESS] ✓ Corner reference image saved: Z:\A.Members\张恩浩\python\transfer\scan_1752063557\scan_1752063557_corner_image.png
[2025-07-09 20:19:28.854] [INFO] [STATUS] Detecting flakes in corner region...
[2025-07-09 20:19:33.532] [INFO] [STATUS] Found 0 flakes in corner region
[2025-07-09 20:19:33.544] [INFO] [STATUS] [20:19:33.543] Creating hybrid corner reference...
[2025-07-09 20:19:33.625] [INFO] [STATUS] [20:19:33.625] Current stage position: (0.00, 0.00) μm
[2025-07-09 20:19:33.637] [INFO] [STATUS] [20:19:33.637] Detecting hybrid features (flakes + edge keypoints)...
[2025-07-09 20:19:34.252] [INFO] [STATUS] [20:19:34.252] Detected 199 total features:
[2025-07-09 20:19:34.279] [INFO] [STATUS] [20:19:34.279]   - 0 flakes
[2025-07-09 20:19:34.289] [INFO] [STATUS] [20:19:34.289]   - 199 edge keypoints
[2025-07-09 20:19:34.301] [INFO] [STATUS] [20:19:34.301] Generating geometric hash table...
[2025-07-09 20:19:58.854] [INFO] [STATUS] [20:19:58.854] Hybrid corner reference created successfully with 199 features
[2025-07-09 20:19:58.886] [INFO] [SUCCESS] ✓ Automatic hybrid corner reference created successfully
[2025-07-09 20:19:58.897] [INFO] [STATUS] DEBUG: About to save reference to: Z:\A.Members\张恩浩\python\transfer\scan_1752063557\scan_1752063557_hybrid_reference.json
[2025-07-09 20:19:58.919] [INFO] [STATUS] DEBUG: Reference data keys: ['format_version', 'alignment_method', 'creation_timestamp', 'corner_origin_abs', 'features', 'hash_table', 'feature_counts', 'detection_methods', 'metadata', 'edge_detection_method', 'creation_mode', 'scan_mode', 'corner_image_file']
