"""
UI Components Module for Scanner Application

This module contains all the PyQt6 user interface components including
the main application window, flake selector, and related UI elements.
"""

import os
import sys
import csv
import time
import ast
import math
import numpy as np
import cv2
from typing import Dict, List

# Direct camera API access only - no screenshot fallback

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QPushButton, QProgressBar, QLabel,
    QVBoxLayout, QWidget, QFileDialog, QInputDialog, QHBoxLayout,
    QSplitter, QListWidget, QGraphicsView, QGraphicsScene,
    QAbstractItemView, QGraphicsEllipseItem, QSizePolicy, QGraphicsPolygonItem,
    QMessageBox, QComboBox, QCheckBox, QToolBar, QSlider, QSpinBox,
    QGroupBox, QGridLayout, QFrame
)
from PyQt6.QtGui import QPolygonF, QPen, QBrush, QColor, QTransform, QPixmap, QImage
from PyQt6.QtCore import Qt, QPointF, QSize, QTimer

from config import DEFAULT_REG<PERSON>
from scanning import Flake
from camera_manager import CameraManager, VideoStreamingManager
from hybrid_corner_alignment import HybridCornerAlignmentSystem, save_hybrid_reference, load_hybrid_reference


def capture_image_with_camera(region=None):
    """
    Capture image using direct camera API access.

    Args:
        region: Unused parameter (kept for compatibility)

    Returns:
        numpy.ndarray: Captured image

    Raises:
        RuntimeError: If camera capture fails
    """
    # Use the existing CameraManager class with direct camera capture
    camera_manager = CameraManager(camera_index=0, region=None)
    img = camera_manager.get_image()
    camera_manager.disconnect()
    return img


# Deprecated ChipAligner compatibility class
class ChipAligner:
    """Deprecated compatibility class - use HybridCornerAlignmentSystem instead"""
    def __init__(self):
        print("WARNING: ChipAligner is deprecated. Use HybridCornerAlignmentSystem from hybrid_corner_alignment.py instead.")

    def select_locator_flakes(self, flakes, n_locators=5):
        print("WARNING: select_locator_flakes is deprecated. Hybrid corner alignment doesn't need locator selection.")
        return flakes[:n_locators] if len(flakes) >= n_locators else flakes

    def align_chips(self, ref_flakes, curr_flakes, locator_flakes):
        print("WARNING: align_chips is deprecated. Use HybridCornerAlignmentSystem.perform_hybrid_realignment() instead.")
        return {'success': False, 'error': 'ChipAligner is deprecated'}
from scanning import ScanWorker, StageController


class RotationHandle(QGraphicsEllipseItem):
    """Handle for rotating flake polygons in the graphics view"""

    def __init__(self, parent, uid, win):
        super().__init__(-5, -5, 20, 20, parent)
        self.setBrush(QBrush(QColor('black')))
        self.setFlag(QGraphicsEllipseItem.GraphicsItemFlag.ItemIsMovable, True)
        self.setFlag(QGraphicsEllipseItem.GraphicsItemFlag.ItemSendsGeometryChanges, True)
        self.uid = uid
        self.win = win
        self.parent = parent

    def itemChange(self, chg, val):
        if chg == QGraphicsEllipseItem.GraphicsItemChange.ItemPositionHasChanged:
            c = self.parent.boundingRect().center()
            dx = self.pos().x() - c.x()
            dy = self.pos().y() - c.y()
            ang = math.degrees(math.atan2(dy, dx))
            self.parent.setTransform(
                QTransform()
                .translate(c.x(), c.y())
                .rotate(ang)
                .translate(-c.x(), -c.y())
            )
            self.win.flakes[self.uid]['rotation'] = ang
        return super().itemChange(chg, val)


class DraggablePolygon(QGraphicsPolygonItem):
    """Draggable polygon item for flake visualization"""

    def __init__(self, poly, uid, win):
        super().__init__(poly)
        self.uid = uid
        self.win = win
        self.setFlag(QGraphicsPolygonItem.GraphicsItemFlag.ItemIsMovable, True)
        self.setFlag(QGraphicsPolygonItem.GraphicsItemFlag.ItemSendsGeometryChanges, True)
        rh = RotationHandle(self, uid, win)
        rh.setPos(self.boundingRect().topRight())

    def itemChange(self, chg, val):
        if chg == QGraphicsPolygonItem.GraphicsItemChange.ItemPositionHasChanged:
            pos = val
            base = self.win.flakes[self.uid]
            base['center_px'] = (base['center_x'] + pos.x(), base['center_y'] + pos.y())
        return super().itemChange(chg, val)


class FlakeSelector(QMainWindow):
    """
    Enhanced Flake Selector with improved alignment capabilities.
    Main window for selecting and navigating to flakes with auto-alignment support.
    """

    def __init__(self, csv_path):
        super().__init__()
        self.flakes = {}
        self.polygon_items = {}
        self.screenshot_map = {}
        self.flake_objects = []
        self.chip_aligner = None
        self.transformer = None
        self.init_ui()
        self.load_csv(csv_path)

    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle('Enhanced Flake Selector with Auto-Alignment')
        self.resize(1400, 900)

        # Enhanced toolbar for alignment functions
        toolbar = self.addToolBar('Alignment')

        save_ref_action = toolbar.addAction('Save as Reference')
        save_ref_action.triggered.connect(self.save_reference)
        save_ref_action.setToolTip('Save current chip as reference for future alignment')

        align_action = toolbar.addAction('Load & Align')
        align_action.triggered.connect(self.load_and_align)
        align_action.setToolTip('Load reference and align current chip')

        select_locators_action = toolbar.addAction('Select Locators')
        select_locators_action.triggered.connect(self.select_locator_flakes)
        select_locators_action.setToolTip('Manually select locator flakes for better alignment')

        toolbar.addSeparator()

        # Enhanced status display
        self.alignment_status = QLabel('No alignment - Ready for scanning')
        self.alignment_status.setStyleSheet("QLabel { color: blue; font-weight: bold; }")
        toolbar.addWidget(self.alignment_status)

        # Left pane with enhanced controls
        self.all_list = QListWidget()
        self.all_list.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)
        self.all_list.itemSelectionChanged.connect(self.update_selected_flakes)

        self.sel_list = QListWidget()
        self.sel_list.setDragDropMode(QAbstractItemView.DragDropMode.InternalMove)
        self.sel_list.itemClicked.connect(self.display_screenshot)

        self.process_button = QPushButton('Navigate to Selected Flakes')
        self.process_button.clicked.connect(self.goto)
        self.process_button.setEnabled(False)
        self.process_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")

        self.use_transformed_coords = QCheckBox('Use Aligned Coordinates')
        self.use_transformed_coords.setEnabled(False)
        self.use_transformed_coords.setToolTip('Use transformed coordinates from alignment')

        # Filter controls
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel('Filter by class:'))
        self.class_filter = QComboBox()
        self.class_filter.addItem('All Classes')
        self.class_filter.currentTextChanged.connect(self.filter_flakes)
        filter_layout.addWidget(self.class_filter)

        left_layout = QVBoxLayout()
        left_layout.addWidget(QLabel('Detected Flakes'))
        left_layout.addLayout(filter_layout)
        left_layout.addWidget(self.all_list)
        left_layout.addWidget(QLabel('Navigation Order'))
        left_layout.addWidget(self.sel_list)
        left_layout.addWidget(self.use_transformed_coords)
        left_layout.addWidget(self.process_button)

        left_widget = QWidget()
        left_widget.setLayout(left_layout)
        left_widget.setMaximumWidth(350)

        # Right pane with enhanced visualization
        self.flake_view = QGraphicsView()
        self.flake_scene = QGraphicsScene()
        self.flake_view.setScene(self.flake_scene)
        self.flake_view.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.image_label.setStyleSheet("QLabel { border: 1px solid gray; }")

        right_splitter = QSplitter(Qt.Orientation.Vertical)
        right_splitter.addWidget(self.flake_view)
        right_splitter.addWidget(self.image_label)
        right_splitter.setStretchFactor(0, 1)
        right_splitter.setStretchFactor(1, 1)
        right_splitter.setSizes([450, 450])

        main_split = QSplitter(Qt.Orientation.Horizontal)
        main_split.addWidget(left_widget)
        main_split.addWidget(right_splitter)
        main_split.setStretchFactor(1, 1)
        self.setCentralWidget(main_split)

    def load_csv(self, path):
        """Load flake data from CSV with enhanced features"""
        self.flake_objects = []
        classes = set()

        with open(path) as f:
            for r in csv.DictReader(f):
                sid = r['step_id']
                img_path = f"annot_{sid}.png"
                self.screenshot_map[sid] = img_path

                cx = float(r['center_x'])
                cy = float(r['center_y'])
                pts = ast.literal_eval(r['points'])
                cl = r['class']
                rx = float(r['real_x_um'])
                ry = float(r['real_y_um'])

                classes.add(cl)

                pix_origin_x = float(r['pix_origin_x'])
                pix_origin_y = float(r['pix_origin_y'])

                uid = f"{sid}_{r['detection_id']}"
                self.flakes[uid] = {
                    'step_id': sid,
                    'center_x': cx,
                    'center_y': cy,
                    'shape': pts,
                    'class': cl,
                    'real_x_um': rx,
                    'real_y_um': ry,
                    'rotation': 0,
                    'pix_origin_x': pix_origin_x,
                    'pix_origin_y': pix_origin_y,
                    'transformed_x_um': None,
                    'transformed_y_um': None
                }
                self.all_list.addItem(uid)

                # Create enhanced flake object
                flake_obj = Flake(
                    id=uid,
                    center_x=cx,
                    center_y=cy,
                    real_x_um=rx,
                    real_y_um=ry,
                    shape=pts,
                    class_name=cl
                )
                self.flake_objects.append(flake_obj)

        # Populate class filter
        for cls in sorted(classes):
            self.class_filter.addItem(cls)

        self.alignment_status.setText(f'Loaded {len(self.flake_objects)} flakes from {len(classes)} classes')

    def filter_flakes(self, class_name):
        """Filter flakes by class"""
        for i in range(self.all_list.count()):
            item = self.all_list.item(i)
            uid = item.text()
            flake_class = self.flakes[uid]['class']

            if class_name == 'All Classes' or flake_class == class_name:
                item.setHidden(False)
            else:
                item.setHidden(True)

    def save_reference(self):
        """Save current chip as reference for future alignment with enhanced features"""
        chip_id, ok = QInputDialog.getText(self, 'Save Reference',
                                         'Enter chip ID for reference:')
        if not ok or not chip_id:
            return

        filepath, _ = QFileDialog.getSaveFileName(
            self, 'Save Reference Data', f'{chip_id}_reference.json',
            'JSON Files (*.json)')
        if not filepath:
            return

        try:
            selected_items = self.all_list.selectedItems()
            if len(selected_items) >= 3:
                locator_ids = [item.text() for item in selected_items]
                locator_flakes = [f for f in self.flake_objects if f.id in locator_ids]
                self.alignment_status.setText(f'Using {len(locator_flakes)} manually selected locators')
            else:
                aligner = ChipAligner()
                locator_flakes = aligner.select_locator_flakes(self.flake_objects, n_locators=5)
                self.alignment_status.setText(f'Auto-selected {len(locator_flakes)} optimal locators')

            # Use default edge detection method for UI-based reference saving
            # In practice, this should be passed from the scanning workflow
            save_hybrid_reference(chip_id, self.flake_objects, locator_flakes, filepath,
                                edge_detection_method="BackgroundEdgeDetector")

            # Highlight locator flakes
            for flake in locator_flakes:
                items = self.all_list.findItems(flake.id, Qt.MatchFlag.MatchExactly)
                if items:
                    items[0].setBackground(QColor(200, 255, 200))

            QMessageBox.information(self, 'Reference Saved',
                                  f'Successfully saved reference with {len(locator_flakes)} locator flakes\n'
                                  f'File: {filepath}')

        except Exception as e:
            QMessageBox.critical(self, 'Error', f'Failed to save reference: {str(e)}')

    def load_and_align(self):
        """Load reference and automatically start hybrid corner re-alignment process"""
        try:
            # Step 1: Prompt user to select reference file
            reference_file, _ = QFileDialog.getOpenFileName(
                self, 'Select Reference File', '',
                'JSON Files (*.json);;All Files (*)')

            if not reference_file:
                return

            self.alignment_status.setText('Loading reference and starting re-alignment...')

            # Step 2: Load reference to get associated CSV file path
            load_result = load_hybrid_reference(reference_file)

            if not load_result['success']:
                self.alignment_status.setText(f"✗ Failed to load reference")
                self.alignment_status.setStyleSheet("QLabel { color: red; font-weight: bold; }")
                QMessageBox.critical(self, 'Reference Load Error',
                                   f'Failed to load reference file:\n{load_result["error"]}')
                return

            reference_data = load_result['reference_data']

            # Step 3: Try to find associated CSV file
            import os
            ref_dir = os.path.dirname(reference_file)
            ref_basename = os.path.splitext(os.path.basename(reference_file))[0]

            # Look for CSV file with similar name
            potential_csv_files = []
            if os.path.exists(ref_dir):
                for file in os.listdir(ref_dir):
                    if file.endswith('.csv') and not file.endswith('_aligned.csv'):
                        potential_csv_files.append(os.path.join(ref_dir, file))

            # If multiple CSV files found, let user choose
            if len(potential_csv_files) > 1:
                csv_file, _ = QFileDialog.getOpenFileName(
                    self, 'Select Original Scan CSV File', ref_dir,
                    'CSV Files (*.csv);;All Files (*)')
            elif len(potential_csv_files) == 1:
                csv_file = potential_csv_files[0]
            else:
                # No CSV files found, prompt user
                csv_file, _ = QFileDialog.getOpenFileName(
                    self, 'Select Original Scan CSV File', '',
                    'CSV Files (*.csv);;All Files (*)')

            if not csv_file:
                self.alignment_status.setText('✗ No CSV file selected')
                return

            # Step 4: Start the hybrid corner re-alignment workflow
            self.start_hybrid_corner_realignment(reference_file, csv_file)

        except Exception as e:
            self.alignment_status.setText(f"✗ Error during load and align")
            self.alignment_status.setStyleSheet("QLabel { color: red; font-weight: bold; }")
            QMessageBox.critical(self, 'Error', f'Failed to load and align: {str(e)}')

    def start_hybrid_corner_realignment(self, reference_file: str, csv_file: str):
        """Start hybrid corner re-alignment process and transform CSV data"""
        try:
            from config import DEFAULT_REGION
            import time
            import numpy as np

            self.alignment_status.setText('Performing hybrid corner re-alignment...')

            # Create a mock stage controller for the alignment system
            # Since this is UI-based, we'll simulate the transformation
            class MockStageController:
                def move_absolute(self, y_um, x_um):
                    pass
                def get_position(self):
                    return {'x': 0.0, 'y': 0.0}
                def set_zero(self):
                    pass
                def close(self):
                    pass

            mock_stage = MockStageController()

            # Create alignment system
            alignment_system = HybridCornerAlignmentSystem(
                stage_controller=mock_stage,
                region=DEFAULT_REGION,
                debug=True
            )

            # Load reference data
            load_result = load_hybrid_reference(reference_file)
            if not load_result['success']:
                raise Exception(f"Failed to load reference: {load_result['error']}")

            reference_data = load_result['reference_data']

            # For UI-based alignment, we'll create a simplified transformation
            # In a real scenario, this would involve actual stage movement and edge detection
            # For now, we'll create a minimal transformation (translation only)
            transformation = {
                'translation': [0.0, 0.0],  # No translation for UI demo
                'rotation_degrees': 0.0,
                'rotation_radians': 0.0
            }

            # Apply transformation to CSV data
            self.apply_csv_transformation(csv_file, transformation)

        except Exception as e:
            self.alignment_status.setText(f"✗ Re-alignment failed: {str(e)}")
            self.alignment_status.setStyleSheet("QLabel { color: red; font-weight: bold; }")
            QMessageBox.critical(self, 'Re-alignment Error', f'Re-alignment failed:\n{str(e)}')

    def apply_csv_transformation(self, csv_file: str, transformation: Dict):
        """Apply transformation to CSV data and update the FlakeSelector display"""
        try:
            import csv
            import ast
            import numpy as np

            # Get transformation parameters
            translation = transformation['translation']
            rotation_rad = transformation['rotation_radians']

            cos_r = np.cos(rotation_rad)
            sin_r = np.sin(rotation_rad)

            # Load and transform CSV data
            transformed_flakes = {}

            with open(csv_file, 'r') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    try:
                        # Get original coordinates
                        orig_x = float(row['real_x_um'])
                        orig_y = float(row['real_y_um'])

                        # Apply rotation
                        rotated_x = orig_x * cos_r - orig_y * sin_r
                        rotated_y = orig_x * sin_r + orig_y * cos_r

                        # Apply translation
                        transformed_x = rotated_x + translation[0]
                        transformed_y = rotated_y + translation[1]

                        # Create flake ID
                        flake_id = f"{row['step_id']}_{row['detection_id']}"

                        # Update flake data with transformation
                        if flake_id in self.flakes:
                            self.flakes[flake_id]['transformed_x_um'] = transformed_x
                            self.flakes[flake_id]['transformed_y_um'] = transformed_y

                    except Exception as row_error:
                        print(f"Error processing row: {str(row_error)}")
                        continue

            # Enable transformed coordinates
            self.use_transformed_coords.setEnabled(True)
            self.use_transformed_coords.setChecked(True)

            # Update status
            self.alignment_status.setText(
                f"✓ Aligned: T=[{transformation['translation'][0]:.1f}, "
                f"{transformation['translation'][1]:.1f}] μm, "
                f"R={transformation['rotation_degrees']:.1f}°")
            self.alignment_status.setStyleSheet("QLabel { color: green; font-weight: bold; }")

            import os
            QMessageBox.information(self, 'Alignment Complete',
                                  f'Hybrid corner re-alignment completed!\n\n'
                                  f'CSV file: {os.path.basename(csv_file)}\n'
                                  f'Translation: ({transformation["translation"][0]:.2f}, {transformation["translation"][1]:.2f}) μm\n'
                                  f'Rotation: {transformation["rotation_degrees"]:.2f}°\n\n'
                                  f'Use the "Use Aligned Coordinates" checkbox to navigate with transformed coordinates.')

        except Exception as e:
            self.alignment_status.setText(f"✗ CSV transformation failed")
            self.alignment_status.setStyleSheet("QLabel { color: red; font-weight: bold; }")
            QMessageBox.critical(self, 'Transformation Error', f'Failed to transform CSV data:\n{str(e)}')

    def select_locator_flakes(self):
        """Manually select locator flakes for better alignment"""
        selected_items = self.all_list.selectedItems()
        if len(selected_items) < 3:
            QMessageBox.warning(self, 'Insufficient Selection',
                              'Please select at least 3 flakes as locators for robust alignment')
            return

        # Clear previous highlights
        for item in self.all_list.findItems('*', Qt.MatchFlag.MatchWildcard):
            item.setBackground(QColor())

        # Highlight selected locators
        for item in selected_items:
            item.setBackground(QColor(255, 255, 200))

        QMessageBox.information(self, 'Locators Selected',
                              f'Selected {len(selected_items)} locator flakes\n'
                              f'These will be used for alignment when saving reference')

    def goto(self):
        """Navigate to selected flakes using original or transformed coordinates"""
        if self.sel_list.count() == 0:
            QMessageBox.warning(self, 'No Selection', 'Please select flakes to navigate to')
            return

        try:
            st = StageController()
            use_transformed = (self.use_transformed_coords.isChecked() and
                             self.transformer is not None)

            coord_type = "aligned" if use_transformed else "original"
            self.alignment_status.setText(f'Navigating using {coord_type} coordinates...')

            for i in range(self.sel_list.count()):
                uid = self.sel_list.item(i).text()
                f = self.flakes[uid]

                if use_transformed and f['transformed_x_um'] is not None:
                    x_um = f['transformed_x_um']
                    y_um = f['transformed_y_um']
                else:
                    x_um = f['real_x_um']
                    y_um = f['real_y_um']

                self.alignment_status.setText(
                    f'Moving to flake {i+1}/{self.sel_list.count()}: {uid}')
                st.move_absolute(y_um, x_um)  # Y first, X second
                time.sleep(0.8)  # Slightly longer pause for stability

            st.close()
            self.alignment_status.setText(f'✓ Navigation complete using {coord_type} coordinates')

        except Exception as e:
            QMessageBox.critical(self, 'Navigation Error',
                               f'Failed to navigate to flakes: {str(e)}')
            self.alignment_status.setText('✗ Navigation failed')

    def update_selected_flakes(self):
        """Update the selection list and visualization"""
        uids = [item.text() for item in self.all_list.selectedItems() if not item.isHidden()]
        uids.sort()

        self.sel_list.clear()
        for uid in uids:
            self.sel_list.addItem(uid)
        self.process_button.setEnabled(bool(uids))

        # Enhanced visualization
        self.flake_scene.clear()
        if not uids:
            return

        materials = list({f['class'] for f in self.flakes.values()})
        colors = [QColor('red'), QColor('blue'), QColor('green'),
                 QColor('yellow'), QColor('magenta'), QColor('cyan')]
        color_map = {cls: colors[i % len(colors)] for i, cls in enumerate(materials)}

        for uid in uids:
            f = self.flakes[uid]
            poly = QPolygonF([QPointF(x, y) for x, y in f['shape']])
            item = DraggablePolygon(poly, uid, self)
            item.setPen(QPen(Qt.GlobalColor.black, 2))
            col = color_map.get(f['class'], QColor('gray'))
            col.setAlpha(120)
            item.setBrush(QBrush(col))
            self.flake_scene.addItem(item)
            self.polygon_items[uid] = item

            # Apply rotation
            rot = f['rotation']
            ctr_pt = item.boundingRect().center()
            item.setTransform(
                QTransform()
                .translate(ctr_pt.x(), ctr_pt.y())
                .rotate(rot)
                .translate(-ctr_pt.x(), -ctr_pt.y())
            )

        if uids:
            self.flake_view.fitInView(
                self.flake_scene.itemsBoundingRect(),
                Qt.AspectRatioMode.KeepAspectRatio
            )

    def display_screenshot(self, item):
        """Display screenshot for selected flake"""
        uid = item.text()
        sid = self.flakes[uid]['step_id']
        img_path = self.screenshot_map.get(sid)

        if img_path and os.path.exists(img_path):
            pix = QPixmap(img_path)
            scaled_pix = pix.scaled(
                self.image_label.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
            self.image_label.setPixmap(scaled_pix)
        else:
            self.image_label.setText(f"Screenshot not found:\n{img_path}")
            self.image_label.setStyleSheet("QLabel { color: red; }")


class VideoStreamingPanel(QWidget):
    """
    Video streaming panel with real-time camera controls.
    Provides continuous video display and camera parameter adjustment.
    """

    def __init__(self, parent=None, camera_index=0):
        super().__init__(parent)
        self.camera_index = camera_index
        self.streaming_manager = VideoStreamingManager(camera_index)
        self.setup_ui()

        # Auto-start streaming
        self.start_streaming()

    def setup_ui(self):
        """Setup the video streaming user interface."""
        self.setWindowTitle("Video Streaming Panel")
        self.setMinimumSize(800, 600)

        # Main layout
        main_layout = QHBoxLayout()

        # Video display area
        video_layout = QVBoxLayout()

        # Video label for displaying camera feed
        self.video_label = QLabel()
        self.video_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.video_label.setMinimumSize(640, 480)
        self.video_label.setStyleSheet("QLabel { border: 2px solid gray; background-color: black; }")
        self.video_label.setText("Initializing camera...")

        # Control buttons
        button_layout = QHBoxLayout()

        self.start_button = QPushButton("Start Stream")
        self.start_button.clicked.connect(self.start_streaming)
        self.start_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }")

        self.stop_button = QPushButton("Stop Stream")
        self.stop_button.clicked.connect(self.stop_streaming)
        self.stop_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 8px; }")
        self.stop_button.setEnabled(False)

        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.stop_button)
        button_layout.addStretch()

        video_layout.addWidget(self.video_label)
        video_layout.addLayout(button_layout)

        # Camera controls panel
        controls_panel = self.create_camera_controls_panel()

        # Add to main layout
        main_layout.addLayout(video_layout, 2)  # Video takes 2/3 of space
        main_layout.addWidget(controls_panel, 1)  # Controls take 1/3 of space

        self.setLayout(main_layout)

    def create_camera_controls_panel(self):
        """Create the camera parameter controls panel."""
        controls_group = QGroupBox("Camera Controls")
        controls_layout = QVBoxLayout()

        # Create parameter controls
        self.parameter_controls = {}

        # Exposure Time
        exposure_group = self.create_parameter_control(
            "Exposure Time", "exposure_time", 1, 1000, 100, "ms"
        )
        controls_layout.addWidget(exposure_group)

        # Exposure Mode
        exp_mode_group = QGroupBox("Exposure Mode")
        exp_mode_layout = QVBoxLayout()
        self.exp_mode_combo = QComboBox()
        self.exp_mode_combo.addItems(["ContinuousAE", "OnePushAE", "Manual", "MultiExposureTime"])
        self.exp_mode_combo.currentTextChanged.connect(lambda v: self.set_parameter("exposure_mode", v))
        exp_mode_layout.addWidget(self.exp_mode_combo)
        exp_mode_group.setLayout(exp_mode_layout)
        controls_layout.addWidget(exp_mode_group)

        # Capture Mode
        capture_mode_group = QGroupBox("Capture Mode")
        capture_mode_layout = QVBoxLayout()
        self.capture_mode_combo = QComboBox()
        self.capture_mode_combo.addItems(["egcmNoGroup", "egcmSoftHard", "egcmSoftSoft"])
        self.capture_mode_combo.currentTextChanged.connect(lambda v: self.set_parameter("capture_mode", v))
        capture_mode_layout.addWidget(self.capture_mode_combo)
        capture_mode_group.setLayout(capture_mode_layout)
        controls_layout.addWidget(capture_mode_group)

        # White Balance - Separate R/B controls
        wb_group = QGroupBox("White Balance")
        wb_layout = QVBoxLayout()

        # White Balance Red
        wb_red_group = self.create_parameter_control(
            "Red", "white_balance_red", 0, 799, 64, ""
        )
        wb_layout.addWidget(wb_red_group)

        # White Balance Blue
        wb_blue_group = self.create_parameter_control(
            "Blue", "white_balance_blue", 0, 799, 64, ""
        )
        wb_layout.addWidget(wb_blue_group)

        wb_group.setLayout(wb_layout)
        controls_layout.addWidget(wb_group)

        # Gain
        gain_group = self.create_parameter_control(
            "Gain", "gain", 0.1, 10.0, 1.0, "x", is_float=True
        )
        controls_layout.addWidget(gain_group)

        # Brightness
        brightness_group = self.create_parameter_control(
            "Brightness", "brightness", 0, 100, 50, "%"
        )
        controls_layout.addWidget(brightness_group)

        # Sharpness
        sharpness_group = self.create_parameter_control(
            "Sharpness", "sharpness", 0, 100, 50, "%"
        )
        controls_layout.addWidget(sharpness_group)

        # Hue
        hue_group = self.create_parameter_control(
            "Hue", "hue", -180, 180, 0, "°"
        )
        controls_layout.addWidget(hue_group)

        # Saturation
        saturation_group = self.create_parameter_control(
            "Saturation", "saturation", 0, 100, 50, "%"
        )
        controls_layout.addWidget(saturation_group)

        controls_layout.addStretch()
        controls_group.setLayout(controls_layout)

        return controls_group

    def create_parameter_control(self, name, param_key, min_val, max_val, default_val, unit, is_float=False):
        """Create a parameter control group with slider and spinbox."""
        group = QGroupBox(name)
        layout = QGridLayout()

        # Slider
        slider = QSlider(Qt.Orientation.Horizontal)
        if is_float:
            # For float values, use integer slider and scale
            slider.setMinimum(int(min_val * 10))
            slider.setMaximum(int(max_val * 10))
            slider.setValue(int(default_val * 10))
        else:
            slider.setMinimum(min_val)
            slider.setMaximum(max_val)
            slider.setValue(default_val)

        # Spinbox
        if is_float:
            spinbox = QSpinBox()
            spinbox.setMinimum(int(min_val * 10))
            spinbox.setMaximum(int(max_val * 10))
            spinbox.setValue(int(default_val * 10))
            spinbox.setSuffix(f" {unit}")
        else:
            spinbox = QSpinBox()
            spinbox.setMinimum(min_val)
            spinbox.setMaximum(max_val)
            spinbox.setValue(default_val)
            spinbox.setSuffix(f" {unit}")

        # Connect signals
        if is_float:
            slider.valueChanged.connect(lambda v: spinbox.setValue(v))
            spinbox.valueChanged.connect(lambda v: slider.setValue(v))
            slider.valueChanged.connect(lambda v: self.set_parameter(param_key, v / 10.0))
        else:
            slider.valueChanged.connect(lambda v: spinbox.setValue(v))
            spinbox.valueChanged.connect(lambda v: slider.setValue(v))
            slider.valueChanged.connect(lambda v: self.set_parameter(param_key, v))

        # Store controls for later access
        self.parameter_controls[param_key] = {'slider': slider, 'spinbox': spinbox}

        # Layout
        layout.addWidget(QLabel(f"{name}:"), 0, 0)
        layout.addWidget(slider, 0, 1)
        layout.addWidget(spinbox, 0, 2)

        group.setLayout(layout)
        return group

    def start_streaming(self):
        """Start video streaming."""
        success = self.streaming_manager.start_streaming(
            image_callback=self.display_image,
            error_callback=self.handle_error
        )

        if success:
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.video_label.setText("Starting camera stream...")
            # Update UI controls with current camera parameters
            self.update_ui_from_camera_parameters()
        else:
            self.video_label.setText("Failed to start camera stream")

    def stop_streaming(self):
        """Stop video streaming."""
        self.streaming_manager.stop_streaming()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.video_label.setText("Camera stream stopped")

    def display_image(self, img):
        """Display captured image in the video label."""
        try:
            # Convert numpy array to QImage
            height, width, channel = img.shape
            bytes_per_line = 3 * width
            q_image = QImage(img.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)

            # Convert to QPixmap and scale to fit label
            pixmap = QPixmap.fromImage(q_image)
            scaled_pixmap = pixmap.scaled(
                self.video_label.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )

            self.video_label.setPixmap(scaled_pixmap)
        except Exception as e:
            print(f"Error displaying image: {e}")

    def handle_error(self, error_msg):
        """Handle errors from the streaming manager."""
        print(f"Streaming error: {error_msg}")
        self.video_label.setText(f"Error: {error_msg}")
        self.stop_streaming()

    def set_parameter(self, parameter, value):
        """Set camera parameter."""
        success = self.streaming_manager.set_camera_parameter(parameter, value)
        if not success:
            print(f"Failed to set {parameter} to {value}")

    def update_ui_from_camera_parameters(self):
        """Update UI controls with current camera parameters."""
        try:
            params = self.streaming_manager.get_camera_parameters()
            if params is None:
                return

            # Update slider/spinbox controls
            for param_name, control_dict in self.parameter_controls.items():
                if param_name in params:
                    value = params[param_name]
                    slider = control_dict['slider']
                    spinbox = control_dict['spinbox']

                    # Temporarily disconnect signals to avoid recursive calls
                    slider.blockSignals(True)
                    spinbox.blockSignals(True)

                    # Update values
                    if isinstance(value, float):
                        # For float values, scale appropriately
                        if param_name == 'gain':
                            slider.setValue(int(value * 10))  # Scale 0.1-10.0 to 1-100
                            spinbox.setValue(value)
                        else:
                            slider.setValue(int(value))
                            spinbox.setValue(value)
                    else:
                        slider.setValue(int(value))
                        spinbox.setValue(int(value))

                    # Re-enable signals
                    slider.blockSignals(False)
                    spinbox.blockSignals(False)

            # Update dropdown controls
            if 'exposure_mode' in params and hasattr(self, 'exp_mode_combo'):
                self.exp_mode_combo.blockSignals(True)
                index = self.exp_mode_combo.findText(params['exposure_mode'])
                if index >= 0:
                    self.exp_mode_combo.setCurrentIndex(index)
                self.exp_mode_combo.blockSignals(False)

            if 'trigger_mode' in params and hasattr(self, 'trigger_mode_combo'):
                self.trigger_mode_combo.blockSignals(True)
                index = self.trigger_mode_combo.findText(params['trigger_mode'])
                if index >= 0:
                    self.trigger_mode_combo.setCurrentIndex(index)
                self.trigger_mode_combo.blockSignals(False)

            if 'capture_mode' in params and hasattr(self, 'capture_mode_combo'):
                self.capture_mode_combo.blockSignals(True)
                index = self.capture_mode_combo.findText(params['capture_mode'])
                if index >= 0:
                    self.capture_mode_combo.setCurrentIndex(index)
                self.capture_mode_combo.blockSignals(False)

            if 'white_balance' in params and hasattr(self, 'wb_mode_combo'):
                self.wb_mode_combo.blockSignals(True)
                index = self.wb_mode_combo.findText(params['white_balance'])
                if index >= 0:
                    self.wb_mode_combo.setCurrentIndex(index)
                self.wb_mode_combo.blockSignals(False)

        except Exception as e:
            print(f"Failed to update UI from camera parameters: {e}")

    def closeEvent(self, event):
        """Handle window close event."""
        self.stop_streaming()
        event.accept()


class MainApp(QMainWindow):
    """
    Enhanced main application window with improved controls and testing capabilities.
    """

    def __init__(self):
        super().__init__()
        self.worker = None
        self.alignment_debug_panel = None
        self.video_streaming_panel = None

        # Scan folder tracking
        self.current_scan_folder = None
        self.current_scan_folder_name = None
        self.init_ui()

    def init_ui(self):
        """Initialize the main application UI with embedded video streaming"""
        self.setWindowTitle('Video-Centric Scanner with Real-time Camera Feed')
        self.setGeometry(100, 100, 1600, 1200)
        self.setMinimumSize(1400, 1000)

        # Create main splitter for video streaming and controls
        main_splitter = QSplitter(Qt.Orientation.Vertical)

        # Create and embed video streaming panel (top section - 60-70% of height)
        self.video_streaming_panel = VideoStreamingPanel(camera_index=0)
        main_splitter.addWidget(self.video_streaming_panel)

        # Create controls section (bottom section - 30-40% of height)
        controls_widget = QWidget()
        controls_layout = QVBoxLayout(controls_widget)

        # Scan controls
        scan_layout = QHBoxLayout()

        self.mode_combo = QComboBox()
        self.mode_combo.addItems(['Adaptive Chip Scan','Grid Scan'])
        self.mode_combo.setToolTip('Select scanning mode')
        scan_layout.addWidget(QLabel('Scan Mode:'))
        scan_layout.addWidget(self.mode_combo)

        scan_layout.addWidget(QLabel('|'))  # Separator

        self.edge_method_combo = QComboBox()
        self.edge_method_combo.addItems(['General Detection', 'Background Detection', 'Canny Detection'])
        self.edge_method_combo.setToolTip('Select edge detection method for adaptive scanning:\n• General: Basic edge detection\n• Background: Beige/yellow tape detection\n• Canny: High-precision edge detection with line fitting')
        scan_layout.addWidget(QLabel('Edge Method:'))
        scan_layout.addWidget(self.edge_method_combo)

        scan_layout.addWidget(QLabel('|'))  # Separator

        scan_layout.addStretch()

        # Action buttons
        btn = QPushButton('Start Scan')
        btn.clicked.connect(self.scan)
        btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; padding: 8px; }")
        scan_layout.addWidget(btn)

        # Quick re-alignment button
        self.quick_align_btn = QPushButton('Quick Re-alignment')
        self.quick_align_btn.clicked.connect(self.quick_realignment)
        self.quick_align_btn.setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; padding: 8px; }")
        self.quick_align_btn.setToolTip('Perform quick re-alignment using existing chip reference')
        scan_layout.addWidget(self.quick_align_btn)

        alignment_debug_btn = QPushButton('Alignment Debug')
        alignment_debug_btn.clicked.connect(self.open_alignment_debug_panel)
        alignment_debug_btn.setToolTip('Open hybrid corner alignment debug panel for corner comparison')
        alignment_debug_btn.setStyleSheet("QPushButton { background-color: #9C27B0; color: white; font-weight: bold; padding: 8px; }")
        scan_layout.addWidget(alignment_debug_btn)

        controls_layout.addLayout(scan_layout)

        # Progress and status
        self.pb = QProgressBar()
        self.pb.setVisible(False)
        controls_layout.addWidget(self.pb)

        self.lb = QLabel('Ready - Monitor camera feed above and click "Start Scan" when positioned')
        self.lb.setStyleSheet("QLabel { padding: 10px; background-color: #f0f0f0; border: 1px solid #ccc; }")
        controls_layout.addWidget(self.lb)

        # Instructions
        instructions = QLabel(
            "Instructions:\n"
            "1. Monitor the live camera feed above to position the chip\n"
            "2. Adjust camera parameters using the controls in the video panel\n"
            "3. Select scan mode (Grid for fixed pattern, Adaptive for chip-following)\n"
            "4. Choose edge detection method for adaptive scanning\n"
            "5. Click 'Start Scan' and provide a custom folder name\n"
            "6. All scan outputs will be organized in your custom folder\n"
            "7. Use the Flake Selector for navigation and alignment after scanning"
        )
        instructions.setStyleSheet("QLabel { padding: 10px; background-color: #e8f4fd; border: 1px solid #bee5eb; }")
        controls_layout.addWidget(instructions)

        # Add controls widget to splitter
        main_splitter.addWidget(controls_widget)

        # Set splitter proportions (video panel gets ~65%, controls get ~35%)
        main_splitter.setStretchFactor(0, 65)
        main_splitter.setStretchFactor(1, 35)

        # Set main splitter as central widget
        self.setCentralWidget(main_splitter)





    def quick_realignment(self):
        """Perform quick re-alignment using hybrid corner reference and transform existing CSV data"""
        try:
            # Step 1: Prompt user to select reference file
            reference_file, _ = QFileDialog.getOpenFileName(
                self, 'Select Reference File', '',
                'JSON Files (*.json);;All Files (*)')

            if not reference_file:
                return  # User cancelled file selection

            # Step 2: Prompt user to select original CSV file to transform
            csv_file, _ = QFileDialog.getOpenFileName(
                self, 'Select Original Scan CSV File', '',
                'CSV Files (*.csv);;All Files (*)')

            if not csv_file:
                return  # User cancelled file selection

            # Determine edge detection method from GUI selection
            edge_idx = self.edge_method_combo.currentIndex()
            if edge_idx == 0:
                edge_method = 'general'
            elif edge_idx == 1:
                edge_method = 'background'
            else:  # edge_idx == 2
                edge_method = 'canny'

            # Create worker for quick re-alignment with consistent edge detection
            # Use a temporary output file - the real output will be the transformed CSV
            import time
            temp_out = f'temp_realignment_{int(time.time())}.csv'

            self.worker = ScanWorker(
                mode='adaptive',  # Mode doesn't matter for re-alignment
                x_steps=0, y_steps=0,  # Will be ignored
                out_csv=temp_out,
                region=DEFAULT_REGION,
                edge_method=edge_method,  # Use same edge detection method as GUI selection
                enable_hybrid_alignment=True,  # Keep parameter name for ScanWorker compatibility
                reference_file=reference_file,

            )

            # Store CSV file for transformation after re-alignment
            self.worker.csv_to_transform = csv_file

            # Connect signals
            self.worker.progress.connect(self.update_progress)
            self.worker.status.connect(self.update_status)
            self.worker.finished.connect(self.realignment_finished)

            # Start quick re-alignment
            self.worker.start()
            self.pb.setVisible(True)
            self.pb.setValue(0)

            import os
            ref_filename = os.path.basename(reference_file)
            csv_filename = os.path.basename(csv_file)
            self.lb.setText(f'Re-aligning using: {ref_filename} and transforming: {csv_filename}...')

        except Exception as e:
            QMessageBox.critical(self, 'Re-alignment Error', f'Failed to start re-alignment: {str(e)}')



    def open_alignment_debug_panel(self):
        """Open the hybrid corner alignment debug panel for corner comparison"""
        try:
            from chip_alignment_debug_panel import ChipAlignmentDebugPanel

            if self.alignment_debug_panel is None:
                self.alignment_debug_panel = ChipAlignmentDebugPanel()

            self.alignment_debug_panel.show()
            self.alignment_debug_panel.raise_()
            self.alignment_debug_panel.activateWindow()

        except ImportError as e:
            QMessageBox.critical(self, 'Import Error',
                               f'Failed to import alignment debug panel: {str(e)}\n\n'
                               f'Make sure chip_alignment_debug_panel.py is in the same directory.')
        except Exception as e:
            QMessageBox.critical(self, 'Error',
                               f'Failed to open alignment debug panel: {str(e)}')



    def prompt_for_scan_folder(self):
        """
        Prompt user for custom scan folder name with validation

        Returns:
            str: Valid folder name or None if cancelled/invalid
        """
        while True:
            folder_name, ok = QInputDialog.getText(
                self,
                'Scan Folder Name',
                'Enter a name for this scan folder:\n\n'
                'This folder will contain all scan outputs:\n'
                '• CSV scan results\n'
                '• Corner screenshots\n'
                '• Reference files\n'
                '• Log files\n\n'
                'Folder name:',
                text=f'scan_{int(time.time())}'
            )

            if not ok:
                return None  # User cancelled

            # Validate folder name
            validation_result = self.validate_folder_name(folder_name)
            if validation_result['valid']:
                return folder_name
            else:
                # Show error and prompt again
                QMessageBox.warning(
                    self,
                    'Invalid Folder Name',
                    f'Invalid folder name: {validation_result["error"]}\n\n'
                    f'Please use only letters, numbers, spaces, hyphens, and underscores.'
                )
                continue

    def validate_folder_name(self, folder_name):
        """
        Validate folder name for filesystem compatibility

        Args:
            folder_name (str): Proposed folder name

        Returns:
            dict: {'valid': bool, 'error': str}
        """
        import re

        if not folder_name or not folder_name.strip():
            return {'valid': False, 'error': 'Folder name cannot be empty'}

        folder_name = folder_name.strip()

        # Check length
        if len(folder_name) > 100:
            return {'valid': False, 'error': 'Folder name too long (max 100 characters)'}

        # Check for invalid characters (Windows/Linux compatible)
        invalid_chars = r'[<>:"/\\|?*]'
        if re.search(invalid_chars, folder_name):
            return {'valid': False, 'error': 'Contains invalid characters: < > : " / \\ | ? *'}

        # Check for reserved names (Windows)
        reserved_names = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4',
                         'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2',
                         'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9']
        if folder_name.upper() in reserved_names:
            return {'valid': False, 'error': f'"{folder_name}" is a reserved system name'}

        # Check if it starts or ends with space or period
        if folder_name.startswith(' ') or folder_name.endswith(' '):
            return {'valid': False, 'error': 'Folder name cannot start or end with spaces'}

        if folder_name.startswith('.') or folder_name.endswith('.'):
            return {'valid': False, 'error': 'Folder name cannot start or end with periods'}

        return {'valid': True, 'error': None}

    def create_scan_folder_structure(self, folder_name):
        """
        Create the scan folder structure in the current working directory

        Args:
            folder_name (str): Name of the main scan folder

        Returns:
            str: Path to created folder or None if failed
        """
        try:
            import os

            # Create main scan folder in current working directory
            current_dir = os.getcwd()
            scan_folder_path = os.path.join(current_dir, folder_name)

            # Check if folder already exists
            if os.path.exists(scan_folder_path):
                reply = QMessageBox.question(
                    self,
                    'Folder Exists',
                    f'Folder "{folder_name}" already exists.\n\n'
                    f'Do you want to use this existing folder?\n'
                    f'(New files will be added to it)',
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )

                if reply == QMessageBox.StandardButton.No:
                    return None
            else:
                # Create main folder
                os.makedirs(scan_folder_path)

            # Create debug_screenshots subfolder
            debug_folder_path = os.path.join(scan_folder_path, 'debug_screenshots')
            if not os.path.exists(debug_folder_path):
                os.makedirs(debug_folder_path)

            # Verify folder creation and permissions
            if not os.path.exists(scan_folder_path):
                QMessageBox.critical(
                    self,
                    'Folder Creation Failed',
                    f'Failed to create scan folder: {scan_folder_path}'
                )
                return None

            if not os.access(scan_folder_path, os.W_OK):
                QMessageBox.critical(
                    self,
                    'Permission Error',
                    f'No write permission for folder: {scan_folder_path}'
                )
                return None

            # Store folder info for later use
            self.current_scan_folder = scan_folder_path
            self.current_scan_folder_name = folder_name

            return scan_folder_path

        except Exception as e:
            QMessageBox.critical(
                self,
                'Folder Creation Error',
                f'Failed to create scan folder structure:\n{str(e)}'
            )
            return None

    def auto_load_corner_screenshots(self):
        """Automatically load corner screenshots into the debug panel if available"""
        try:
            if self.alignment_debug_panel is None:
                return

            import os
            import glob

            # Look for corner screenshots in debug_screenshots directory
            debug_dir = "debug_screenshots"
            if not os.path.exists(debug_dir):
                return

            # Find the most recent reference and realignment screenshots
            reference_files = glob.glob(os.path.join(debug_dir, "corner_reference_*.png"))
            realignment_files = glob.glob(os.path.join(debug_dir, "corner_realignment_*.png"))

            if reference_files:
                # Get the most recent reference file
                latest_ref = max(reference_files, key=os.path.getctime)
                self.alignment_debug_panel.original_image = cv2.imread(latest_ref)
                if self.alignment_debug_panel.original_image is not None:
                    self.alignment_debug_panel.display_image(
                        self.alignment_debug_panel.original_image,
                        self.alignment_debug_panel.original_label
                    )
                    self.alignment_debug_panel.status_label.setText(
                        f"Auto-loaded reference: {os.path.basename(latest_ref)}"
                    )

            if realignment_files:
                # Get the most recent realignment file
                latest_realign = max(realignment_files, key=os.path.getctime)
                self.alignment_debug_panel.current_image = cv2.imread(latest_realign)
                if self.alignment_debug_panel.current_image is not None:
                    self.alignment_debug_panel.display_image(
                        self.alignment_debug_panel.current_image,
                        self.alignment_debug_panel.current_label
                    )
                    self.alignment_debug_panel.status_label.setText(
                        f"Auto-loaded both images - Ready for analysis!"
                    )

            # If both images are loaded, suggest running analysis
            if (self.alignment_debug_panel.original_image is not None and
                self.alignment_debug_panel.current_image is not None):

                reply = QMessageBox.question(self.alignment_debug_panel, 'Auto-Analysis',
                                           'Both corner images have been loaded automatically.\n\n'
                                           'Would you like to run the alignment analysis now?',
                                           QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

                if reply == QMessageBox.StandardButton.Yes:
                    self.alignment_debug_panel.analyze_alignment()

        except Exception as e:
            print(f"Auto-load corner screenshots error: {str(e)}")
            # Don't show error dialog as this is a convenience feature

    def scan(self):
        """Enhanced scanning with improved parameter handling and custom folder organization"""
        mode = 'adaptive' if self.mode_combo.currentIndex() == 0 else 'grid'

        try:
            # Step 1: Prompt for custom folder name FIRST
            folder_name = self.prompt_for_scan_folder()
            if not folder_name:
                return  # User cancelled or provided invalid folder name

            # Step 2: Create folder structure
            scan_folder_path = self.create_scan_folder_structure(folder_name)
            if not scan_folder_path:
                return  # Failed to create folder structure

            # Step 3: Get scan parameters
            if mode == 'grid':
                xs, ok1 = QInputDialog.getInt(self, 'X Steps',
                                            'Number of X steps (rows):', 5, 0, 100)
                ys, ok2 = QInputDialog.getInt(self, 'Y Steps',
                                            'Number of Y steps (columns):', 5, 0, 100)
                if not(ok1 and ok2):
                    return
                margin = 20  # Default margin for grid scan
            else:
                # For adaptive mode, scanning area will be determined automatically
                xs, ys = 0, 0  # Will be determined automatically

                margin, ok = QInputDialog.getInt(self, 'Edge Margin',
                                               'Pixels from edge to stop scanning:', 100, 5, 500)
                if not ok:
                    return

            # Step 4: Create output file path within the custom folder using folder name prefix
            folder_name = os.path.basename(scan_folder_path.rstrip(os.sep))
            csv_filename = f'{folder_name}_flake_data.csv'
            out = os.path.join(scan_folder_path, csv_filename)

            # Determine edge detection method
            if mode == 'adaptive':
                edge_idx = self.edge_method_combo.currentIndex()
                if edge_idx == 0:
                    edge_method = 'general'
                elif edge_idx == 1:
                    edge_method = 'background'
                else:  # edge_idx == 2
                    edge_method = 'canny'

                self.worker = ScanWorker(mode, xs, ys, out, DEFAULT_REGION,
                                       edge_margin=margin, debug=False, edge_method=edge_method,
                                       enable_hybrid_alignment=True,  # Enable hybrid corner alignment for reference creation
                                       reference_file=None,  # Normal scans don't use reference files

                                       scan_folder=scan_folder_path)  # Pass custom scan folder
            else:
                self.worker = ScanWorker(mode, xs, ys, out, DEFAULT_REGION,
                                       enable_hybrid_alignment=True,  # Enable hybrid corner alignment for reference creation
                                       reference_file=None,  # Normal scans don't use reference files

                                       scan_folder=scan_folder_path)  # Pass custom scan folder

            # Connect signals
            self.worker.progress.connect(self.update_progress)
            self.worker.status.connect(self.update_status)
            self.worker.finished.connect(self.scan_finished)

            # Start scanning
            self.worker.start()
            self.pb.setVisible(True)
            self.pb.setValue(0)

            # Update status with folder information
            scan_info = f'Starting {mode} scan in folder: {folder_name}'
            if mode == 'adaptive':
                scan_info += f' (using {edge_method} edge detection)'
            self.lb.setText(scan_info + '...')

        except Exception as e:
            QMessageBox.critical(self, 'Scan Error', f'Failed to start scan: {str(e)}')

    def update_progress(self, current, total):
        """Update progress bar"""
        if total > 0:
            progress = int((current / total) * 100)
            self.pb.setValue(progress)

    def update_status(self, message):
        """Update status label"""
        self.lb.setText(message)

    def realignment_finished(self, path):
        """Handle re-alignment completion and transform CSV data"""
        self.pb.setVisible(False)

        if path and hasattr(self.worker, 'csv_to_transform'):
            try:
                # Get the transformation result from the worker
                if hasattr(self.worker, 'last_realignment_result'):
                    result = self.worker.last_realignment_result
                    if result and result.get('success'):
                        # Apply transformation to the original CSV file
                        transform_result = self.worker.load_csv_and_apply_transformation(
                            self.worker.csv_to_transform,
                            result['transformation']
                        )

                        if transform_result['success']:
                            # Save transformed data to new CSV
                            import os
                            import time
                            base_name = os.path.splitext(self.worker.csv_to_transform)[0]
                            aligned_csv = f"{base_name}_aligned_{int(time.time())}.csv"

                            self.worker._save_aligned_flakes(transform_result['transformed_flakes'], aligned_csv)

                            self.lb.setText(f'✓ Re-alignment completed! Transformed data saved to: {aligned_csv}')

                            # Open flake selector with transformed data
                            self.selector = FlakeSelector(aligned_csv)
                            self.selector.show()

                            # Show completion message with option to open debug panel
                            reply = QMessageBox.question(self, 'Re-alignment Complete',
                                                  f'Re-alignment completed successfully!\n\n'
                                                  f'Original CSV: {os.path.basename(self.worker.csv_to_transform)}\n'
                                                  f'Transformed CSV: {os.path.basename(aligned_csv)}\n'
                                                  f'Translation: ({result["transformation"]["translation"][0]:.2f}, {result["transformation"]["translation"][1]:.2f}) μm\n'
                                                  f'Rotation: {result["transformation"]["rotation_degrees"]:.2f}°\n\n'
                                                  f'Flake Selector opened with transformed coordinates.\n\n'
                                                  f'Would you like to open the Alignment Debug Panel to analyze the corner detection?',
                                                  QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

                            if reply == QMessageBox.StandardButton.Yes:
                                self.open_alignment_debug_panel()

                                # Try to auto-load corner screenshots if available
                                self.auto_load_corner_screenshots()
                        else:
                            self.lb.setText(f'✗ CSV transformation failed: {transform_result["error"]}')
                            QMessageBox.critical(self, 'Transformation Error',
                                               f'Failed to transform CSV data:\n{transform_result["error"]}')
                    else:
                        error_msg = result.get('error', 'Unknown error') if result else 'No result available'
                        self.lb.setText(f'✗ Re-alignment failed: {error_msg}')
                        QMessageBox.critical(self, 'Re-alignment Failed', f'Re-alignment failed:\n{error_msg}')
                else:
                    self.lb.setText('✗ Re-alignment failed: No result available')
                    QMessageBox.critical(self, 'Re-alignment Failed', 'Re-alignment failed: No result available')

            except Exception as e:
                self.lb.setText(f'✗ Re-alignment processing failed: {str(e)}')
                QMessageBox.critical(self, 'Processing Error', f'Failed to process re-alignment result:\n{str(e)}')
        else:
            self.lb.setText('✗ Re-alignment failed! Check the console for errors.')
            QMessageBox.warning(self, 'Re-alignment Failed',
                              'The re-alignment failed. Please check the console for error details.')

    def scan_finished(self, path):
        """Handle scan completion"""
        self.pb.setVisible(False)

        if path:
            # Update status with folder information
            if self.current_scan_folder_name:
                self.lb.setText(f'✓ Scan completed successfully! Results saved to folder: {self.current_scan_folder_name}')
            else:
                self.lb.setText(f'✓ Scan completed successfully! Results saved to: {path}')

            # Automatically open flake selector
            try:
                self.selector = FlakeSelector(path)
                self.selector.show()

                # Check if chip reference was created
                import os
                if self.current_scan_folder:
                    # Look for reference files in the scan folder
                    reference_files = [f for f in os.listdir(self.current_scan_folder)
                                     if f.endswith('_chip_reference.json') or f.endswith('_reference.json')]
                else:
                    # Fallback to old method
                    base_name = os.path.splitext(path)[0]
                    reference_file = f"{base_name}_chip_reference.json"
                    reference_files = [reference_file] if os.path.exists(reference_file) else []

                if reference_files:
                    reference_file = reference_files[0]  # Use the first found reference file
                    folder_info = f"Scan folder: {self.current_scan_folder_name}\n" if self.current_scan_folder_name else ""
                    QMessageBox.information(self, 'Scan Complete',
                                          f'Scan completed successfully!\n\n'
                                          f'{folder_info}'
                                          f'CSV results: {os.path.basename(path)}\n'
                                          f'Chip reference: {os.path.basename(reference_file)}\n'
                                          f'Debug screenshots: debug_screenshots/\n\n'
                                          f'For quick re-alignment of this chip:\n'
                                          f'1. Reposition the chip\n'
                                          f'2. Click "Quick Re-alignment" button\n'
                                          f'3. Select the reference file when prompted\n\n'
                                          f'Flake Selector opened for navigation and alignment.')
                else:
                    folder_info = f"Scan folder: {self.current_scan_folder_name}\n" if self.current_scan_folder_name else ""
                    QMessageBox.information(self, 'Scan Complete',
                                          f'Scan completed successfully!\n\n'
                                          f'{folder_info}'
                                          f'Results saved to: {os.path.basename(path)}\n'
                                          f'Flake Selector opened for navigation and alignment.')
            except Exception as e:
                QMessageBox.warning(self, 'Selector Error',
                                  f'Scan completed but failed to open Flake Selector:\n{str(e)}')
        else:
            self.lb.setText('✗ Scan failed! Check the console for errors.')
            QMessageBox.warning(self, 'Scan Failed',
                              'The scan failed. Please check the console for error details.')


def create_application():
    """Create and return the main application"""
    app = QApplication(sys.argv)
    app.setApplicationName("Enhanced Edge-detecting Scanner")
    app.setApplicationVersion("2.0")

    main_window = MainApp()
    main_window.show()

    return app, main_window