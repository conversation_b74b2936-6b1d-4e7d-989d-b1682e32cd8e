#!/usr/bin/env python3
"""
Test Geometric Hash Performance Optimization

Test script to verify that the geometric hashing performance issues have been resolved
and that reference file generation completes in reasonable time with manageable file sizes.
"""

import sys
import time
import os
import json
import random
from typing import List
from PyQt6.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt6.QtCore import QTimer, QThread, pyqtSignal

from hybrid_corner_alignment import HybridFeature, GeometricHasher, save_hybrid_reference


class GeometricHashPerformanceTestWorker(QThread):
    """Worker thread to test geometric hash performance."""
    
    statusUpdate = pyqtSignal(str)
    
    def __init__(self, test_type="feature_scaling"):
        super().__init__()
        self.test_type = test_type
        self.stopped = False
        
    def run(self):
        """Run geometric hash performance test."""
        try:
            if self.test_type == "feature_scaling":
                self.test_feature_scaling()
            elif self.test_type == "optimization_comparison":
                self.test_optimization_comparison()
            elif self.test_type == "file_size_analysis":
                self.test_file_size_analysis()
        except Exception as e:
            self.statusUpdate.emit(f"✗ {self.test_type} test failed: {e}")
            import traceback
            traceback.print_exc()
    
    def _generate_test_features(self, count: int, region_size: float = 1000.0) -> List[HybridFeature]:
        """Generate test features for performance testing."""
        features = []
        for i in range(count):
            # Generate random features within region
            x = random.uniform(0, region_size)
            y = random.uniform(0, region_size)
            confidence = random.uniform(0.5, 1.0)
            
            feature = HybridFeature(
                id=f"test_feature_{i}",
                feature_type="flake" if i % 2 == 0 else "edge_keypoint",
                x_um=x,
                y_um=y,
                confidence=confidence,
                size_um=random.uniform(5.0, 20.0),
                metadata={}
            )
            features.append(feature)
        
        return features
    
    def test_feature_scaling(self):
        """Test performance scaling with different feature counts."""
        self.statusUpdate.emit("Testing geometric hash performance scaling...")
        
        feature_counts = [10, 25, 50, 100, 200, 500]
        
        for count in feature_counts:
            if self.stopped:
                break
                
            self.statusUpdate.emit(f"Testing with {count} features...")
            
            # Generate test features
            features = self._generate_test_features(count)
            
            # Test with optimized hasher
            hasher = GeometricHasher(
                hash_precision=0,
                debug=False,
                max_features=100,
                max_triplets=5000,
                spatial_threshold=300.0
            )
            
            start_time = time.time()
            hash_table = hasher.generate_hash_table(features)
            duration = time.time() - start_time
            
            total_triplets = sum(len(bucket) for bucket in hash_table.values())
            
            self.statusUpdate.emit(f"  {count} features → {total_triplets} triplets in {duration:.3f}s")
            
            if duration > 10.0:  # More than 10 seconds is too slow
                self.statusUpdate.emit(f"⚠ Performance warning: {duration:.1f}s is too slow for {count} features")
    
    def test_optimization_comparison(self):
        """Compare optimized vs unoptimized performance."""
        self.statusUpdate.emit("Comparing optimized vs unoptimized performance...")
        
        test_feature_count = 150
        features = self._generate_test_features(test_feature_count)
        
        # Test unoptimized (high limits)
        self.statusUpdate.emit(f"Testing unoptimized hasher with {test_feature_count} features...")
        unoptimized_hasher = GeometricHasher(
            hash_precision=0,
            debug=False,
            max_features=1000,      # Very high limit
            max_triplets=100000,    # Very high limit
            spatial_threshold=2000.0  # Very large threshold
        )
        
        start_time = time.time()
        unoptimized_table = unoptimized_hasher.generate_hash_table(features)
        unoptimized_duration = time.time() - start_time
        unoptimized_triplets = sum(len(bucket) for bucket in unoptimized_table.values())
        
        # Test optimized (reasonable limits)
        self.statusUpdate.emit(f"Testing optimized hasher with {test_feature_count} features...")
        optimized_hasher = GeometricHasher(
            hash_precision=0,
            debug=False,
            max_features=100,
            max_triplets=5000,
            spatial_threshold=300.0
        )
        
        start_time = time.time()
        optimized_table = optimized_hasher.generate_hash_table(features)
        optimized_duration = time.time() - start_time
        optimized_triplets = sum(len(bucket) for bucket in optimized_table.values())
        
        # Compare results
        speedup = unoptimized_duration / max(optimized_duration, 0.001)
        triplet_reduction = (unoptimized_triplets - optimized_triplets) / max(unoptimized_triplets, 1) * 100
        
        self.statusUpdate.emit(f"Performance comparison results:")
        self.statusUpdate.emit(f"  Unoptimized: {unoptimized_triplets} triplets in {unoptimized_duration:.2f}s")
        self.statusUpdate.emit(f"  Optimized: {optimized_triplets} triplets in {optimized_duration:.2f}s")
        self.statusUpdate.emit(f"  Speedup: {speedup:.1f}x faster")
        self.statusUpdate.emit(f"  Triplet reduction: {triplet_reduction:.1f}%")
    
    def test_file_size_analysis(self):
        """Test reference file size with different configurations."""
        self.statusUpdate.emit("Testing reference file size analysis...")
        
        test_feature_count = 100
        features = self._generate_test_features(test_feature_count)
        
        configurations = [
            {"name": "Minimal", "max_features": 50, "max_triplets": 1000},
            {"name": "Balanced", "max_features": 100, "max_triplets": 5000},
            {"name": "Comprehensive", "max_features": 150, "max_triplets": 10000},
        ]
        
        for config in configurations:
            if self.stopped:
                break
                
            self.statusUpdate.emit(f"Testing {config['name']} configuration...")
            
            hasher = GeometricHasher(
                hash_precision=0,
                debug=False,
                max_features=config['max_features'],
                max_triplets=config['max_triplets'],
                spatial_threshold=300.0
            )
            
            # Generate hash table
            start_time = time.time()
            hash_table = hasher.generate_hash_table(features)
            hash_duration = time.time() - start_time
            
            # Create reference data
            reference_data = {
                'format_version': '3.0',
                'creation_timestamp': time.time(),
                'features': [
                    {
                        'id': f.id,
                        'type': f.feature_type,
                        'x_um': f.x_um,
                        'y_um': f.y_um,
                        'confidence': f.confidence
                    } for f in features[:config['max_features']]
                ],
                'hash_table': hash_table,
                'metadata': {
                    'total_features': len(features),
                    'selected_features': min(len(features), config['max_features']),
                    'max_triplets': config['max_triplets']
                }
            }
            
            # Save to temporary file
            temp_filepath = f"temp_reference_{config['name'].lower()}.json"
            
            save_start_time = time.time()
            success = save_hybrid_reference(reference_data, temp_filepath)
            save_duration = time.time() - save_start_time
            
            if success and os.path.exists(temp_filepath):
                file_size = os.path.getsize(temp_filepath)
                file_size_mb = file_size / (1024 * 1024)
                total_triplets = sum(len(bucket) for bucket in hash_table.values())
                
                self.statusUpdate.emit(f"  {config['name']} results:")
                self.statusUpdate.emit(f"    - Hash generation: {hash_duration:.2f}s")
                self.statusUpdate.emit(f"    - File save: {save_duration:.2f}s")
                self.statusUpdate.emit(f"    - File size: {file_size_mb:.2f} MB")
                self.statusUpdate.emit(f"    - Triplets: {total_triplets}")
                
                # Clean up
                try:
                    os.remove(temp_filepath)
                except:
                    pass
            else:
                self.statusUpdate.emit(f"  ✗ Failed to save {config['name']} configuration")
    
    def stop(self):
        """Stop the test worker."""
        self.stopped = True


class GeometricHashPerformanceTestWindow(QMainWindow):
    """Test window for geometric hash performance."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Geometric Hash Performance Test")
        self.setGeometry(100, 100, 900, 700)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Status label
        self.status_label = QLabel("Ready to test geometric hash performance optimizations...")
        layout.addWidget(self.status_label)
        
        # Test buttons
        scaling_button = QPushButton("Test Feature Scaling Performance")
        scaling_button.clicked.connect(lambda: self.run_test("feature_scaling"))
        layout.addWidget(scaling_button)
        
        comparison_button = QPushButton("Test Optimization Comparison")
        comparison_button.clicked.connect(lambda: self.run_test("optimization_comparison"))
        layout.addWidget(comparison_button)
        
        filesize_button = QPushButton("Test File Size Analysis")
        filesize_button.clicked.connect(lambda: self.run_test("file_size_analysis"))
        layout.addWidget(filesize_button)
        
        # Output text area
        self.output_text = QTextEdit()
        self.output_text.setReadOnly(True)
        layout.addWidget(self.output_text)
        
        # Test worker
        self.test_worker = None
        
    def run_test(self, test_type):
        """Run a specific geometric hash performance test."""
        if self.test_worker and self.test_worker.isRunning():
            self.test_worker.stop()
            self.test_worker.wait()
        
        self.output_text.clear()
        self.status_label.setText(f"Running {test_type} test...")
        
        self.test_worker = GeometricHashPerformanceTestWorker(test_type)
        self.test_worker.statusUpdate.connect(self.update_output)
        self.test_worker.finished.connect(lambda: self.status_label.setText("Test completed"))
        self.test_worker.start()
    
    def update_output(self, message):
        """Update the output text area."""
        self.output_text.append(message)
        
    def closeEvent(self, event):
        """Handle window close event."""
        if self.test_worker and self.test_worker.isRunning():
            self.test_worker.stop()
            self.test_worker.wait()
        event.accept()


def main():
    """Main function to run the geometric hash performance test."""
    app = QApplication(sys.argv)
    
    # Create and show test window
    window = GeometricHashPerformanceTestWindow()
    window.show()
    
    print("Geometric Hash Performance Optimization Test")
    print("=" * 50)
    print("This test verifies:")
    print("1. Feature scaling performance with different counts")
    print("2. Optimization comparison (before vs after)")
    print("3. Reference file size analysis with different configurations")
    print("4. Resolution of exponential complexity issues")
    print()
    print("Key optimizations implemented:")
    print("- Feature selection limiting (max 100 features)")
    print("- Triplet count limiting (max 5000 triplets)")
    print("- Spatial filtering (300μm threshold)")
    print("- Compact JSON serialization")
    print("- Performance monitoring and logging")
    print()
    print("Expected results:")
    print("- Reference generation under 30 seconds")
    print("- File sizes under 50MB")
    print("- Maintained alignment accuracy")
    print()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
