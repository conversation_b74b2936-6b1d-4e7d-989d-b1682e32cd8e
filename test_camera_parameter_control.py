#!/usr/bin/env python3
"""
Test Camera Parameter Control

Test script to verify the camera parameter control functionality
in the VideoStreamingPanel and CentralizedCameraManager.
"""

import sys
import time
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt6.QtCore import QTimer

from camera_manager import CentralizedCameraManager, VideoStreamingManager
from ui_components import VideoStreamingPanel


class CameraParameterTestWindow(QMainWindow):
    """Test window for camera parameter control functionality."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Camera Parameter Control Test")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Status label
        self.status_label = QLabel("Initializing camera parameter test...")
        layout.addWidget(self.status_label)
        
        # Test buttons
        test_button = QPushButton("Run Parameter Tests")
        test_button.clicked.connect(self.run_parameter_tests)
        layout.addWidget(test_button)
        
        # Video streaming panel
        self.video_panel = VideoStreamingPanel(camera_index=0)
        layout.addWidget(self.video_panel)
        
        # Initialize managers
        self.centralized_manager = CentralizedCameraManager(camera_index=0)
        self.streaming_manager = VideoStreamingManager(camera_index=0)
        
        # Timer for periodic parameter reading
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_parameter_status)
        self.timer.start(5000)  # Update every 5 seconds
        
    def run_parameter_tests(self):
        """Run comprehensive parameter control tests."""
        self.status_label.setText("Running parameter tests...")
        
        try:
            # Test 1: Parameter validation
            self.status_label.setText("Test 1: Parameter validation...")
            
            # Test valid parameters
            test_params = [
                ('exposure_time', 100),
                ('gain', 2.0),
                ('brightness', 75),
                ('exposure_mode', 'Manual'),
                ('trigger_mode', 'Soft'),
                ('white_balance_red', 128),
                ('white_balance_blue', 128),
            ]
            
            for param, value in test_params:
                success = self.centralized_manager.set_camera_parameter(param, value)
                print(f"Set {param} = {value}: {'✓' if success else '✗'}")
                time.sleep(0.1)  # Brief delay between parameter changes
            
            # Test 2: Parameter reading
            self.status_label.setText("Test 2: Parameter reading...")
            params = self.centralized_manager.get_camera_parameters()
            if params:
                print("Current camera parameters:")
                for param, value in params.items():
                    print(f"  {param}: {value}")
            else:
                print("Failed to read camera parameters")
            
            # Test 3: Invalid parameter handling
            self.status_label.setText("Test 3: Invalid parameter handling...")
            
            invalid_tests = [
                ('exposure_time', -10),  # Negative value
                ('gain', 50.0),  # Out of range
                ('unknown_param', 100),  # Unknown parameter
                ('exposure_mode', 'InvalidMode'),  # Invalid enum value
            ]
            
            for param, value in invalid_tests:
                success = self.centralized_manager.set_camera_parameter(param, value)
                print(f"Set invalid {param} = {value}: {'✗ (expected)' if not success else '⚠ (unexpected success)'}")
            
            # Test 4: UI update test
            self.status_label.setText("Test 4: UI parameter update...")
            self.video_panel.update_ui_from_camera_parameters()
            
            self.status_label.setText("✓ All parameter tests completed! Check console for detailed results.")
            
        except Exception as e:
            self.status_label.setText(f"✗ Test failed: {e}")
            print(f"Parameter test error: {e}")
            import traceback
            traceback.print_exc()
    
    def update_parameter_status(self):
        """Periodically update parameter status."""
        try:
            params = self.centralized_manager.get_camera_parameters()
            if params:
                # Show a few key parameters in the status
                exposure = params.get('exposure_time', 'N/A')
                gain = params.get('gain', 'N/A')
                mode = params.get('exposure_mode', 'N/A')
                self.status_label.setText(
                    f"Camera Status - Exposure: {exposure}ms, Gain: {gain}x, Mode: {mode}"
                )
            else:
                self.status_label.setText("Camera parameters not available")
        except Exception as e:
            self.status_label.setText(f"Status update error: {e}")
    
    def closeEvent(self, event):
        """Handle window close event."""
        self.timer.stop()
        self.video_panel.stop_streaming()
        event.accept()


def main():
    """Main function to run the camera parameter test."""
    app = QApplication(sys.argv)
    
    # Create and show test window
    window = CameraParameterTestWindow()
    window.show()
    
    print("Camera Parameter Control Test")
    print("=" * 40)
    print("This test verifies:")
    print("1. Parameter validation and range checking")
    print("2. Camera parameter setting via PyNikonSciCam API")
    print("3. Parameter reading and round-trip verification")
    print("4. UI control updates")
    print("5. Error handling for invalid parameters")
    print()
    print("Click 'Run Parameter Tests' to start testing.")
    print("The video panel should show live camera feed with functional controls.")
    print()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
