================================================================================
SCANNING OPERATION LOG - 2025-07-09 19:31:50
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752060708\scan_1752060708_flake_data.csv
Grid Steps: 0 x 0
Edge Method: background
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752060708
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752060708\debug_screenshots
================================================================================

[2025-07-09 19:31:50.424] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-09 19:31:50.435] [INFO] [SYSTEM] Using custom scan folder: scan_1752060708
[2025-07-09 19:31:50.454] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-09 19:31:50.581] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-09 19:31:50.596] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-09 19:31:50.606] [INFO] [STATUS] ✗ SCAN ABORTED: Unexpected error in corner detection: 'CentralizedCameraManager' object has no attribute 'get_image'
[2025-07-09 19:31:50.616] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1752060708\scan_1752060708_flake_data.csv
[2025-07-09 19:31:50.625] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-09 19:31:50.637] [INFO] [STATUS] Scanning for annotated images...
[2025-07-09 19:31:50.653] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
