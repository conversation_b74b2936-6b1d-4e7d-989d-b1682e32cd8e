================================================================================
SCANNING OPERATION LOG - 2025-07-09 18:28:37
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752056912\scan_1752056912_flake_data.csv
Grid Steps: 0 x 0
Edge Method: general
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752056912
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752056912\debug_screenshots
================================================================================

[2025-07-09 18:28:37.068] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-09 18:28:37.078] [INFO] [SYSTEM] Using custom scan folder: scan_1752056912
[2025-07-09 18:28:37.096] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-09 18:28:37.238] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-09 18:28:37.249] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-09 18:28:37.259] [INFO] [STATUS] ✗ SCAN ABORTED: Unexpected error in corner detection: 'NoneType' object has no attribute 'shape'
[2025-07-09 18:28:37.271] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1752056912\scan_1752056912_flake_data.csv
[2025-07-09 18:28:37.284] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-09 18:28:37.295] [INFO] [STATUS] Scanning for annotated images...
[2025-07-09 18:28:37.317] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
