================================================================================
SCANNING OPERATION LOG - 2025-07-08 18:42:59
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1751971376\scan_1751971376_flake_data.csv
Grid Steps: 0 x 0
Edge Method: general
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1751971376
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1751971376\debug_screenshots
================================================================================

[2025-07-08 18:42:59.496] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-08 18:42:59.506] [INFO] [SYSTEM] Using custom scan folder: scan_1751971376
[2025-07-08 18:42:59.534] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-08 18:42:59.694] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-08 18:42:59.705] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-08 18:42:59.715] [INFO] [STATUS] ✗ SCAN ABORTED: Camera not available - PyNikonSciCam not installed or camera not detected
[2025-07-08 18:42:59.726] [INFO] [STATUS] ✗ Cannot proceed without a valid hybrid corner reference
[2025-07-08 18:42:59.737] [INFO] [STATUS] ✗ Please check:
[2025-07-08 18:42:59.748] [INFO] [STATUS]   → Chip positioning and visibility
[2025-07-08 18:42:59.757] [INFO] [STATUS]   → Edge detection parameters
[2025-07-08 18:42:59.767] [INFO] [STATUS]   → Image quality and lighting
[2025-07-08 18:42:59.777] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1751971376\scan_1751971376_flake_data.csv
[2025-07-08 18:42:59.786] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-08 18:42:59.796] [INFO] [STATUS] Scanning for annotated images...
[2025-07-08 18:42:59.807] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
