================================================================================
SCANNING OPERATION LOG - 2025-07-09 20:31:16
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752064267\scan_1752064267_flake_data.csv
Grid Steps: 0 x 0
Edge Method: background
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752064267
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752064267\debug_screenshots
================================================================================

[2025-07-09 20:31:16.453] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-09 20:31:16.463] [INFO] [SYSTEM] Using custom scan folder: scan_1752064267
[2025-07-09 20:31:16.478] [INFO] [SUCCESS] ✓ Camera streaming already active
[2025-07-09 20:31:16.496] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-09 20:31:16.621] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-09 20:31:16.632] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-09 20:31:17.006] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-07-09 20:31:18.381] [INFO] [POSITION] Position feedback: (9.73, 0.00) μm
[2025-07-09 20:31:20.280] [INFO] [POSITION] Position feedback: (182.42, 0.00) μm
[2025-07-09 20:31:22.170] [INFO] [POSITION] Position feedback: (363.15, 0.00) μm
[2025-07-09 20:31:23.930] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-07-09 20:31:24.100] [INFO] [POSITION] Position feedback: (518.27, 0.00) μm
[2025-07-09 20:31:24.312] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-07-09 20:31:25.526] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-07-09 20:31:25.700] [INFO] [POSITION] Position feedback: (518.27, 0.00) μm
[2025-07-09 20:31:26.212] [INFO] [WORKFLOW] Starting position (rotation-robust): (0.0, 518.4) μm
[2025-07-09 20:31:26.230] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-07-09 20:31:26.369] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-07-09 20:31:26.380] [INFO] [STATUS] Creating automatic hybrid corner reference...
[2025-07-09 20:31:26.390] [INFO] [STATUS] Creating hybrid corner reference from current position...
[2025-07-09 20:31:26.485] [INFO] [SUCCESS] ✓ Corner reference image saved: Z:\A.Members\张恩浩\python\transfer\scan_1752064267\scan_1752064267_corner_image.png
[2025-07-09 20:31:26.494] [INFO] [STATUS] Detecting flakes in corner region...
[2025-07-09 20:31:30.743] [INFO] [STATUS] Found 0 flakes in corner region
[2025-07-09 20:31:30.757] [INFO] [STATUS] [20:31:30.757] Creating hybrid corner reference...
[2025-07-09 20:31:30.839] [INFO] [STATUS] [20:31:30.839] Current stage position: (0.00, 0.00) μm
[2025-07-09 20:31:30.849] [INFO] [STATUS] [20:31:30.849] Detecting hybrid features (flakes + edge keypoints)...
[2025-07-09 20:31:30.948] [INFO] [STATUS] [20:31:30.948] Detected 149 total features:
[2025-07-09 20:31:30.968] [INFO] [STATUS] [20:31:30.968]   - 0 flakes
[2025-07-09 20:31:30.978] [INFO] [STATUS] [20:31:30.978]   - 149 edge keypoints
[2025-07-09 20:31:30.987] [INFO] [STATUS] [20:31:30.987] Generating geometric hash table...
[2025-07-09 20:31:44.486] [INFO] [STATUS] [20:31:44.486] Hybrid corner reference created successfully with 149 features
[2025-07-09 20:31:44.513] [INFO] [SUCCESS] ✓ Automatic hybrid corner reference created successfully
[2025-07-09 20:31:44.523] [INFO] [STATUS] DEBUG: About to save reference to: Z:\A.Members\张恩浩\python\transfer\scan_1752064267\scan_1752064267_hybrid_reference.json
[2025-07-09 20:31:44.535] [INFO] [STATUS] DEBUG: Reference data keys: ['format_version', 'alignment_method', 'creation_timestamp', 'corner_origin_abs', 'features', 'hash_table', 'feature_counts', 'detection_methods', 'metadata', 'edge_detection_method', 'creation_mode', 'scan_mode', 'corner_image_file']
