================================================================================
SCANNING OPERATION LOG - 2025-07-09 18:35:42
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\aaa\aaa_flake_data.csv
Grid Steps: 0 x 0
Edge Method: background
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\aaa
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\aaa\debug_screenshots
================================================================================

[2025-07-09 18:35:42.296] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-09 18:35:42.306] [INFO] [SYSTEM] Using custom scan folder: aaa
[2025-07-09 18:35:42.318] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-09 18:35:42.437] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-09 18:35:42.447] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-09 18:35:42.528] [ERROR] [ERROR] ✗ SCAN ABORTED: Camera capture failed: Failed to send command. Error code: -10
[2025-07-09 18:35:42.539] [INFO] [STATUS] ✗ Cannot proceed without a valid hybrid corner reference
[2025-07-09 18:35:42.549] [INFO] [STATUS] ✗ Please check:
[2025-07-09 18:35:42.559] [INFO] [STATUS]   → Chip positioning and visibility
[2025-07-09 18:35:42.569] [INFO] [STATUS]   → Edge detection parameters
[2025-07-09 18:35:42.579] [INFO] [STATUS]   → Image quality and lighting
[2025-07-09 18:35:42.589] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\aaa\aaa_flake_data.csv
[2025-07-09 18:35:42.599] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-09 18:35:42.611] [INFO] [STATUS] Scanning for annotated images...
[2025-07-09 18:35:42.622] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
