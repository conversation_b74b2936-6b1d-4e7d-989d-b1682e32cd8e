#!/usr/bin/env python3
"""
Test script for video streaming integration.

This script tests the refactored video streaming system to ensure:
1. Camera backend integration works correctly
2. GUI components function properly
3. Integration with existing scanning workflows is maintained
4. mss package dependencies have been completely removed
"""

import sys
import os
import time
import traceback
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt6.QtCore import QTimer

# Test imports
def test_imports():
    """Test that all required modules can be imported without mss dependencies."""
    print("Testing imports...")
    
    try:
        # Test camera manager imports
        from camera_manager import CameraManager, VideoStreamingManager, NikonCameraExtended, CameraWorker
        print("✓ Camera manager imports successful")
        
        # Test UI components imports
        from ui_components import VideoStreamingPanel, MainApp
        print("✓ UI components imports successful")
        
        # Test that mss is not imported anywhere
        import sys
        mss_modules = [name for name in sys.modules.keys() if 'mss' in name.lower()]
        if mss_modules:
            print(f"⚠ Warning: mss-related modules found: {mss_modules}")
        else:
            print("✓ No mss modules detected")
            
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error during imports: {e}")
        return False


def test_camera_manager():
    """Test camera manager functionality."""
    print("\nTesting camera manager...")
    
    try:
        from camera_manager import CameraManager, CAMERA_AVAILABLE
        
        if not CAMERA_AVAILABLE:
            print("⚠ PyNikonSciCam not available - skipping camera tests")
            return True
            
        # Test camera manager initialization
        camera_manager = CameraManager(camera_index=3, test_camera=False)  # Use simulator
        print("✓ Camera manager initialization successful")
        
        # Test image capture (this might fail if no camera/simulator available)
        try:
            img = camera_manager.get_image()
            print(f"✓ Image capture successful, shape: {img.shape}")
        except Exception as e:
            print(f"⚠ Image capture failed (expected if no camera): {e}")
            
        return True
        
    except Exception as e:
        print(f"✗ Camera manager test failed: {e}")
        traceback.print_exc()
        return False


def test_video_streaming_manager():
    """Test video streaming manager functionality."""
    print("\nTesting video streaming manager...")
    
    try:
        from camera_manager import VideoStreamingManager, CAMERA_AVAILABLE
        
        if not CAMERA_AVAILABLE:
            print("⚠ PyNikonSciCam not available - skipping streaming tests")
            return True
            
        # Test streaming manager initialization
        streaming_manager = VideoStreamingManager(camera_index=3)  # Use simulator
        print("✓ Video streaming manager initialization successful")
        
        # Test parameter methods
        params = streaming_manager.get_camera_parameters()
        if params:
            print(f"✓ Camera parameters retrieved: {list(params.keys())}")
        else:
            print("⚠ Camera parameters not available")
            
        # Test parameter setting
        success = streaming_manager.set_camera_parameter("exposure_time", 100)
        print(f"✓ Parameter setting test: {'success' if success else 'failed (expected)'}")
        
        return True
        
    except Exception as e:
        print(f"✗ Video streaming manager test failed: {e}")
        traceback.print_exc()
        return False


class TestVideoStreamingPanel(QMainWindow):
    """Test window for video streaming panel."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Video Streaming Integration Test")
        self.setGeometry(100, 100, 1000, 700)
        
        # Main widget
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        # Status label
        self.status_label = QLabel("Initializing video streaming test...")
        layout.addWidget(self.status_label)
        
        # Test button
        test_btn = QPushButton("Test Video Streaming Panel")
        test_btn.clicked.connect(self.test_streaming_panel)
        layout.addWidget(test_btn)
        
        # Timer for auto-close
        self.timer = QTimer()
        self.timer.timeout.connect(self.close)
        
    def test_streaming_panel(self):
        """Test the video streaming panel."""
        try:
            from ui_components import VideoStreamingPanel
            
            self.status_label.setText("Opening video streaming panel...")
            
            # Create and show video streaming panel
            self.streaming_panel = VideoStreamingPanel(camera_index=3)  # Use simulator
            self.streaming_panel.show()
            
            self.status_label.setText("✓ Video streaming panel opened successfully!\nPanel will auto-close in 5 seconds...")
            
            # Auto-close after 5 seconds
            self.timer.start(5000)
            
        except Exception as e:
            self.status_label.setText(f"✗ Video streaming panel test failed: {e}")
            traceback.print_exc()


def test_gui_integration():
    """Test GUI integration."""
    print("\nTesting GUI integration...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            
        # Test video streaming panel creation
        test_window = TestVideoStreamingPanel()
        test_window.show()
        
        print("✓ GUI integration test window created")
        print("  - Window will display for 10 seconds")
        print("  - Click 'Test Video Streaming Panel' to test streaming panel")
        
        # Auto-close after 10 seconds
        QTimer.singleShot(10000, app.quit)
        
        # Run for a short time
        app.processEvents()
        
        return True
        
    except Exception as e:
        print(f"✗ GUI integration test failed: {e}")
        traceback.print_exc()
        return False


def test_main_app_integration():
    """Test integration with main application."""
    print("\nTesting main app integration...")
    
    try:
        from ui_components import MainApp
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            
        # Create main app instance
        main_app = MainApp()
        print("✓ Main app creation successful")
        
        # Check if video streaming button exists
        if hasattr(main_app, 'video_streaming_panel'):
            print("✓ Video streaming panel attribute found in MainApp")
        else:
            print("⚠ Video streaming panel attribute not found in MainApp")
            
        return True
        
    except Exception as e:
        print(f"✗ Main app integration test failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all integration tests."""
    print("=" * 60)
    print("Video Streaming Integration Test Suite")
    print("=" * 60)
    
    tests = [
        ("Import Tests", test_imports),
        ("Camera Manager Tests", test_camera_manager),
        ("Video Streaming Manager Tests", test_video_streaming_manager),
        ("Main App Integration Tests", test_main_app_integration),
        ("GUI Integration Tests", test_gui_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test {test_name} crashed: {e}")
            traceback.print_exc()
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'=' * 60}")
    print("Test Results Summary")
    print(f"{'=' * 60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Video streaming integration is working correctly.")
    else:
        print("⚠ Some tests failed. Please check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
