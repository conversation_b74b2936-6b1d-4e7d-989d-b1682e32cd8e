================================================================================
SCANNING OPERATION LOG - 2025-07-09 18:30:50
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752057046\scan_1752057046_flake_data.csv
Grid Steps: 0 x 0
Edge Method: general
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752057046
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752057046\debug_screenshots
================================================================================

[2025-07-09 18:30:50.061] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-09 18:30:50.071] [INFO] [SYSTEM] Using custom scan folder: scan_1752057046
[2025-07-09 18:30:50.089] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-09 18:30:50.216] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-09 18:30:50.234] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-09 18:30:50.273] [ERROR] [ERROR] ✗ SCAN ABORTED: Camera capture failed: Failed to send command. Error code: -10
[2025-07-09 18:30:50.284] [INFO] [STATUS] ✗ Cannot proceed without a valid hybrid corner reference
[2025-07-09 18:30:50.294] [INFO] [STATUS] ✗ Please check:
[2025-07-09 18:30:50.305] [INFO] [STATUS]   → Chip positioning and visibility
[2025-07-09 18:30:50.315] [INFO] [STATUS]   → Edge detection parameters
[2025-07-09 18:30:50.325] [INFO] [STATUS]   → Image quality and lighting
[2025-07-09 18:30:50.334] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1752057046\scan_1752057046_flake_data.csv
[2025-07-09 18:30:50.344] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-09 18:30:50.354] [INFO] [STATUS] Scanning for annotated images...
[2025-07-09 18:30:50.366] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
