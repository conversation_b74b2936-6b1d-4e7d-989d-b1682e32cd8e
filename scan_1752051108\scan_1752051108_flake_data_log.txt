================================================================================
SCANNING OPERATION LOG - 2025-07-09 16:51:50
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752051108\scan_1752051108_flake_data.csv
Grid Steps: 0 x 0
Edge Method: general
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752051108
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752051108\debug_screenshots
================================================================================

[2025-07-09 16:51:50.159] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-09 16:51:50.169] [INFO] [SYSTEM] Using custom scan folder: scan_1752051108
[2025-07-09 16:51:50.185] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-09 16:51:50.325] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-09 16:51:50.335] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-09 16:51:50.360] [INFO] [STATUS] ✗ SCAN ABORTED: Unexpected error in corner detection: 'NoneType' object has no attribute 'shape'
[2025-07-09 16:51:50.380] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1752051108\scan_1752051108_flake_data.csv
[2025-07-09 16:51:50.389] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-09 16:51:50.397] [INFO] [STATUS] Scanning for annotated images...
[2025-07-09 16:51:50.407] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
