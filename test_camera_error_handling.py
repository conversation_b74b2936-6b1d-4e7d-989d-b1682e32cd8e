#!/usr/bin/env python3
"""
Test Camera Error Handling

Test script to verify the enhanced error handling and diagnostics
in CentralizedCameraManager to resolve PyNikonSciCam Error code: -10.
"""

import sys
import time
import threading
from PyQt6.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt6.QtCore import QTimer, QThread, pyqtSignal

from camera_manager import CentralizedCameraManager, VideoStreamingManager


class CameraErrorTestWorker(QThread):
    """Worker thread to test camera error handling scenarios."""
    
    statusUpdate = pyqtSignal(str)
    
    def __init__(self, test_type="basic"):
        super().__init__()
        self.test_type = test_type
        self.stopped = False
        
    def run(self):
        """Run camera error handling test."""
        try:
            if self.test_type == "basic":
                self.test_basic_capture()
            elif self.test_type == "stress":
                self.test_stress_capture()
            elif self.test_type == "concurrent":
                self.test_concurrent_access()
            elif self.test_type == "recovery":
                self.test_error_recovery()
        except Exception as e:
            self.statusUpdate.emit(f"✗ {self.test_type} test failed: {e}")
            import traceback
            traceback.print_exc()
    
    def test_basic_capture(self):
        """Test basic camera capture with enhanced error handling."""
        self.statusUpdate.emit("Testing basic camera capture with enhanced error handling...")
        
        camera_manager = CentralizedCameraManager(camera_index=0)
        
        for i in range(5):
            if self.stopped:
                break
                
            try:
                self.statusUpdate.emit(f"Attempting capture {i+1}/5...")
                img = camera_manager.get_image(timeout=10.0, max_retries=2)
                if img is not None:
                    self.statusUpdate.emit(f"✓ Capture {i+1}/5 successful: {img.shape}")
                else:
                    self.statusUpdate.emit(f"⚠ Capture {i+1}/5 returned None")
                time.sleep(1.0)
            except Exception as e:
                self.statusUpdate.emit(f"✗ Capture {i+1}/5 failed: {e}")
    
    def test_stress_capture(self):
        """Test rapid camera capture to stress test error handling."""
        self.statusUpdate.emit("Testing rapid camera capture (stress test)...")
        
        camera_manager = CentralizedCameraManager(camera_index=0)
        
        for i in range(10):
            if self.stopped:
                break
                
            try:
                self.statusUpdate.emit(f"Rapid capture {i+1}/10...")
                img = camera_manager.get_image(timeout=5.0, max_retries=1)
                if img is not None:
                    self.statusUpdate.emit(f"✓ Rapid capture {i+1}/10 successful")
                else:
                    self.statusUpdate.emit(f"⚠ Rapid capture {i+1}/10 returned None")
                time.sleep(0.2)  # Rapid capture
            except Exception as e:
                self.statusUpdate.emit(f"✗ Rapid capture {i+1}/10 failed: {e}")
    
    def test_concurrent_access(self):
        """Test concurrent camera access with streaming."""
        self.statusUpdate.emit("Testing concurrent camera access with streaming...")
        
        # Start video streaming
        streaming_manager = VideoStreamingManager(camera_index=0)
        camera_manager = CentralizedCameraManager(camera_index=0)
        
        def image_callback(img):
            pass  # Just consume the images
            
        def error_callback(error):
            self.statusUpdate.emit(f"⚠ Streaming error: {error}")
        
        streaming_started = streaming_manager.start_streaming(
            image_callback=image_callback,
            error_callback=error_callback
        )
        
        if streaming_started:
            self.statusUpdate.emit("✓ Video streaming started")
            
            # Wait for streaming to stabilize
            time.sleep(2.0)
            
            # Now try to capture images while streaming is active
            for i in range(5):
                if self.stopped:
                    break
                    
                try:
                    self.statusUpdate.emit(f"Concurrent capture {i+1}/5 during streaming...")
                    img = camera_manager.get_image(timeout=10.0, max_retries=2)
                    if img is not None:
                        self.statusUpdate.emit(f"✓ Concurrent capture {i+1}/5 successful")
                    else:
                        self.statusUpdate.emit(f"⚠ Concurrent capture {i+1}/5 returned None")
                    time.sleep(1.0)
                except Exception as e:
                    self.statusUpdate.emit(f"✗ Concurrent capture {i+1}/5 failed: {e}")
            
            # Stop streaming
            streaming_manager.stop_streaming()
            self.statusUpdate.emit("✓ Video streaming stopped")
        else:
            self.statusUpdate.emit("✗ Failed to start video streaming")
    
    def test_error_recovery(self):
        """Test error recovery scenarios."""
        self.statusUpdate.emit("Testing error recovery scenarios...")
        
        camera_manager = CentralizedCameraManager(camera_index=0)
        
        # Test multiple captures to verify recovery
        for i in range(3):
            if self.stopped:
                break
                
            try:
                self.statusUpdate.emit(f"Recovery test {i+1}/3...")
                img = camera_manager.get_image(timeout=15.0, max_retries=3)
                if img is not None:
                    self.statusUpdate.emit(f"✓ Recovery test {i+1}/3 successful: {img.shape}")
                else:
                    self.statusUpdate.emit(f"⚠ Recovery test {i+1}/3 returned None")
                time.sleep(2.0)
            except Exception as e:
                self.statusUpdate.emit(f"✗ Recovery test {i+1}/3 failed: {e}")
    
    def stop(self):
        """Stop the test worker."""
        self.stopped = True


class CameraErrorTestWindow(QMainWindow):
    """Test window for camera error handling."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Camera Error Handling Test")
        self.setGeometry(100, 100, 900, 700)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Status label
        self.status_label = QLabel("Ready to test camera error handling...")
        layout.addWidget(self.status_label)
        
        # Test buttons
        basic_button = QPushButton("Test Basic Capture")
        basic_button.clicked.connect(lambda: self.run_test("basic"))
        layout.addWidget(basic_button)
        
        stress_button = QPushButton("Test Stress Capture")
        stress_button.clicked.connect(lambda: self.run_test("stress"))
        layout.addWidget(stress_button)
        
        concurrent_button = QPushButton("Test Concurrent Access")
        concurrent_button.clicked.connect(lambda: self.run_test("concurrent"))
        layout.addWidget(concurrent_button)
        
        recovery_button = QPushButton("Test Error Recovery")
        recovery_button.clicked.connect(lambda: self.run_test("recovery"))
        layout.addWidget(recovery_button)
        
        # Output text area
        self.output_text = QTextEdit()
        self.output_text.setReadOnly(True)
        layout.addWidget(self.output_text)
        
        # Test worker
        self.test_worker = None
        
    def run_test(self, test_type):
        """Run a specific camera error handling test."""
        if self.test_worker and self.test_worker.isRunning():
            self.test_worker.stop()
            self.test_worker.wait()
        
        self.output_text.clear()
        self.status_label.setText(f"Running {test_type} test...")
        
        self.test_worker = CameraErrorTestWorker(test_type)
        self.test_worker.statusUpdate.connect(self.update_output)
        self.test_worker.finished.connect(lambda: self.status_label.setText("Test completed"))
        self.test_worker.start()
    
    def update_output(self, message):
        """Update the output text area."""
        self.output_text.append(message)
        
    def closeEvent(self, event):
        """Handle window close event."""
        if self.test_worker and self.test_worker.isRunning():
            self.test_worker.stop()
            self.test_worker.wait()
        event.accept()


def main():
    """Main function to run the camera error handling test."""
    app = QApplication(sys.argv)
    
    # Create and show test window
    window = CameraErrorTestWindow()
    window.show()
    
    print("Camera Error Handling Test")
    print("=" * 40)
    print("This test verifies:")
    print("1. Enhanced error handling in CentralizedCameraManager")
    print("2. Camera state validation and recovery")
    print("3. Retry logic with exponential backoff")
    print("4. Concurrent access coordination")
    print("5. Resolution of PyNikonSciCam Error code: -10")
    print()
    print("Click the test buttons to verify enhanced error handling.")
    print("Check console output for detailed diagnostics.")
    print()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
