#!/usr/bin/env python3
"""
Test script for the refactored video-centric interface.

This script tests the new MainApp interface to ensure:
1. Video streaming is embedded as a core component
2. Deprecated testing buttons have been removed
3. Layout structure is correct with proper proportions
4. All scanning functionality remains intact
"""

import sys
import os
import time
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import <PERSON><PERSON>ime<PERSON>

def test_main_app_structure():
    """Test the structure of the refactored MainApp."""
    print("Testing MainApp structure...")
    
    try:
        from ui_components import MainApp
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            
        # Create main app instance
        main_app = MainApp()
        print("✓ MainApp creation successful")
        
        # Check window properties
        geometry = main_app.geometry()
        print(f"✓ Window size: {geometry.width()}x{geometry.height()}")
        
        if geometry.width() >= 1400 and geometry.height() >= 1000:
            print("✓ Window size meets minimum requirements")
        else:
            print(f"⚠ Window size may be too small: {geometry.width()}x{geometry.height()}")
        
        # Check if video streaming panel is embedded
        if hasattr(main_app, 'video_streaming_panel') and main_app.video_streaming_panel is not None:
            print("✓ Video streaming panel is embedded in MainApp")
        else:
            print("✗ Video streaming panel not found in MainApp")
            return False
            
        # Check central widget structure
        central_widget = main_app.centralWidget()
        if central_widget is not None:
            print("✓ Central widget exists")
            
            # Check if it's a splitter (should be for our new layout)
            from PyQt6.QtWidgets import QSplitter
            if isinstance(central_widget, QSplitter):
                print("✓ Central widget is a splitter (correct layout structure)")
                
                # Check splitter orientation and widget count
                if central_widget.orientation().name == 'Vertical':
                    print("✓ Splitter has vertical orientation")
                else:
                    print("⚠ Splitter orientation is not vertical")
                    
                widget_count = central_widget.count()
                print(f"✓ Splitter contains {widget_count} widgets")
                
                if widget_count == 2:
                    print("✓ Correct number of widgets in splitter (video + controls)")
                else:
                    print(f"⚠ Expected 2 widgets in splitter, found {widget_count}")
                    
            else:
                print("⚠ Central widget is not a splitter")
        else:
            print("✗ No central widget found")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ MainApp structure test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_deprecated_methods_removed():
    """Test that deprecated methods have been removed."""
    print("\nTesting deprecated method removal...")
    
    try:
        from ui_components import MainApp
        
        # Check that deprecated methods are not present
        deprecated_methods = [
            'test_edge_detection',
            'test_alignment', 
            'open_debug_panel',
            'open_video_streaming_panel'
        ]
        
        all_removed = True
        for method_name in deprecated_methods:
            if hasattr(MainApp, method_name):
                print(f"✗ Deprecated method '{method_name}' still exists")
                all_removed = False
            else:
                print(f"✓ Deprecated method '{method_name}' successfully removed")
                
        return all_removed
        
    except Exception as e:
        print(f"✗ Deprecated method removal test failed: {e}")
        return False


def test_video_streaming_integration():
    """Test video streaming integration."""
    print("\nTesting video streaming integration...")
    
    try:
        from ui_components import MainApp, VideoStreamingPanel
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            
        # Create main app
        main_app = MainApp()
        
        # Check video streaming panel
        video_panel = main_app.video_streaming_panel
        if video_panel is not None:
            print("✓ Video streaming panel exists")
            
            # Check if it's the correct type
            if isinstance(video_panel, VideoStreamingPanel):
                print("✓ Video panel is correct type (VideoStreamingPanel)")
            else:
                print(f"⚠ Video panel is wrong type: {type(video_panel)}")
                
            # Check if streaming manager exists
            if hasattr(video_panel, 'streaming_manager'):
                print("✓ Video panel has streaming manager")
            else:
                print("⚠ Video panel missing streaming manager")
                
            # Check if parameter controls exist
            if hasattr(video_panel, 'parameter_controls'):
                print("✓ Video panel has parameter controls")
                control_count = len(video_panel.parameter_controls)
                print(f"✓ Found {control_count} parameter controls")
            else:
                print("⚠ Video panel missing parameter controls")
                
        else:
            print("✗ Video streaming panel not found")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ Video streaming integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_scanning_functionality_preserved():
    """Test that scanning functionality is preserved."""
    print("\nTesting scanning functionality preservation...")
    
    try:
        from ui_components import MainApp
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            
        # Create main app
        main_app = MainApp()
        
        # Check essential scanning methods still exist
        essential_methods = [
            'scan',
            'quick_realignment',
            'open_alignment_debug_panel',
            'prompt_for_scan_folder',
            'update_progress',
            'update_status'
        ]
        
        all_present = True
        for method_name in essential_methods:
            if hasattr(main_app, method_name):
                print(f"✓ Essential method '{method_name}' preserved")
            else:
                print(f"✗ Essential method '{method_name}' missing")
                all_present = False
                
        # Check essential attributes
        essential_attributes = [
            'mode_combo',
            'edge_method_combo', 
            'quick_align_btn',
            'pb',  # progress bar
            'lb'   # status label
        ]
        
        for attr_name in essential_attributes:
            if hasattr(main_app, attr_name):
                print(f"✓ Essential attribute '{attr_name}' preserved")
            else:
                print(f"✗ Essential attribute '{attr_name}' missing")
                all_present = False
                
        return all_present
        
    except Exception as e:
        print(f"✗ Scanning functionality preservation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_visual_interface():
    """Test the visual interface by showing the window briefly."""
    print("\nTesting visual interface...")
    
    try:
        from ui_components import MainApp
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            
        # Create and show main app
        main_app = MainApp()
        main_app.show()
        
        print("✓ MainApp window displayed successfully")
        print("  - Video streaming panel should be visible in the top section")
        print("  - Scan controls should be visible in the bottom section")
        print("  - Window will auto-close in 8 seconds")
        
        # Auto-close after 8 seconds
        QTimer.singleShot(8000, app.quit)
        
        # Process events briefly to show the window
        for _ in range(100):
            app.processEvents()
            time.sleep(0.01)
            
        return True
        
    except Exception as e:
        print(f"✗ Visual interface test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests for the video-centric interface."""
    print("=" * 60)
    print("Video-Centric Interface Test Suite")
    print("=" * 60)
    
    tests = [
        ("MainApp Structure", test_main_app_structure),
        ("Deprecated Methods Removal", test_deprecated_methods_removed),
        ("Video Streaming Integration", test_video_streaming_integration),
        ("Scanning Functionality Preservation", test_scanning_functionality_preserved),
        ("Visual Interface", test_visual_interface),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test {test_name} crashed: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'=' * 60}")
    print("Test Results Summary")
    print(f"{'=' * 60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{test_name:<35} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Video-centric interface is working correctly.")
        print("\nKey improvements verified:")
        print("  ✓ Video streaming is now a core, persistent component")
        print("  ✓ Deprecated testing buttons have been removed")
        print("  ✓ Layout properly divides video (top) and controls (bottom)")
        print("  ✓ All scanning functionality is preserved")
        print("  ✓ Window size is appropriate for embedded video streaming")
    else:
        print("⚠ Some tests failed. Please check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
