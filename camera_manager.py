#!/usr/bin/env python3
"""
Camera Manager Module

Centralized camera access manager that resolves PyNikonSciCam's concurrent access limitations.
Implements a shared camera instance with proper locking/queuing mechanisms for both
continuous video streaming and on-demand scanning operations.

This module provides thread-safe camera access coordination between video streaming
and scanning operations, ensuring reliable camera access for all components.
"""

import time
import threading
import queue
import numpy as np
from PyQt6.QtCore import QThread, pyqtSignal, QMutex, QWaitCondition

# Camera imports
try:
    from PyNikonSciCam import NikonCamera, constants as consts, methods
    CAMERA_AVAILABLE = True
except ImportError:
    CAMERA_AVAILABLE = False
    print("⚠ PyNikonSciCam not available - camera functionality disabled")


class CentralizedCameraManager:
    """
    Centralized camera access manager that resolves PyNikonSciCam's concurrent access limitations.

    This singleton class manages a single camera connection shared across all components,
    implementing proper locking/queuing mechanisms for both continuous streaming and
    on-demand scanning operations.
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls, camera_index=0):
        """Singleton pattern to ensure only one camera manager exists."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self, camera_index=0):
        """Initialize the centralized camera manager."""
        if self._initialized:
            return

        self.camera_index = camera_index
        self.camera = None
        self.camera_lock = threading.RLock()
        self.streaming_active = False
        self.streaming_paused = False
        self.pause_condition = threading.Condition(self.camera_lock)
        self.operation_queue = queue.Queue()
        self.use_camera = CAMERA_AVAILABLE

        # Initialize camera connection
        if self.use_camera:
            self._initialize_camera()

        self._initialized = True

    def _initialize_camera(self):
        """Initialize the camera connection."""
        try:
            if CAMERA_AVAILABLE:
                self.camera = NikonCameraExtended(self.camera_index)
                print(f"✓ Centralized camera manager initialized with camera ID {self.camera_index}")
            else:
                raise RuntimeError("PyNikonSciCam not available")
        except Exception as e:
            print(f"⚠ Camera initialization failed: {e}")
            self.use_camera = False
            self.camera = None

    # def get_image(self, timeout=5.0):
    #     """
    #     Capture a single image, pausing streaming if necessary.

    #     Args:
    #         timeout: Maximum time to wait for camera access

    #     Returns:
    #         numpy.ndarray: Captured image

    #     Raises:
    #         RuntimeError: If camera is not available or capture fails
    #     """
    #     if not self.use_camera or self.camera is None:
    #         raise RuntimeError("Camera not available")

    #     with self.camera_lock:
    #         try:
    #             # If streaming is active, pause it temporarily
    #             was_streaming = False
    #             if self.streaming_active and not self.streaming_paused:
    #                 self._pause_streaming_internal()
    #                 was_streaming = True

    #             # Capture image
    #             img = self.camera.get_image()
    #             if img is None:
    #                 raise RuntimeError("Camera returned None image")

    #             # Resume streaming if it was active
    #             if was_streaming:
    #                 self._resume_streaming_internal()

    #             return img

    #         except Exception as e:
    #             # Resume streaming on error if it was active
    #             if was_streaming and self.streaming_paused:
    #                 self._resume_streaming_internal()
    #             raise RuntimeError(f"Camera capture failed: {e}")

    def start_streaming(self):
        """Start continuous streaming mode."""
        if not self.use_camera or self.camera is None:
            return False

        with self.camera_lock:
            if not self.streaming_active:
                try:
                    self.camera.start_live()
                    self.streaming_active = True
                    self.streaming_paused = False
                    print("✓ Camera streaming started")
                    return True
                except Exception as e:
                    print(f"⚠ Failed to start streaming: {e}")
                    return False
            return True

    def stop_streaming(self):
        """Stop continuous streaming mode."""
        if not self.use_camera or self.camera is None:
            return

        with self.camera_lock:
            if self.streaming_active:
                try:
                    self.camera.stop_live()
                    self.streaming_active = False
                    self.streaming_paused = False
                    print("✓ Camera streaming stopped")
                except Exception as e:
                    print(f"⚠ Failed to stop streaming: {e}")

    def get_streaming_image(self):
        """
        Get the latest streaming image without interrupting the stream.

        Returns:
            numpy.ndarray or None: Latest streaming image
        """
        if not self.use_camera or self.camera is None or not self.streaming_active:
            return None

        with self.camera_lock:
            if self.streaming_paused:
                return None

            try:
                return self.camera.get_live_image()
            except Exception:
                return None

    def _pause_streaming_internal(self):
        """Internal method to pause streaming (must be called with lock held)."""
        if self.streaming_active and not self.streaming_paused:
            try:
                self.camera.stop_live()
                self.streaming_paused = True
                print("⚠ Streaming paused for camera operation")
            except Exception as e:
                print(f"⚠ Failed to pause streaming: {e}")

    def _resume_streaming_internal(self):
        """Internal method to resume streaming (must be called with lock held)."""
        if self.streaming_active and self.streaming_paused:
            try:
                self.camera.start_live()
                self.streaming_paused = False
                print("✓ Streaming resumed")
                self.pause_condition.notify_all()
            except Exception as e:
                print(f"⚠ Failed to resume streaming: {e}")

    def set_camera_parameter(self, parameter, value):
        """
        Set camera parameter, pausing streaming if necessary.

        Args:
            parameter: Parameter name
            value: Parameter value

        Returns:
            bool: True if parameter was set successfully
        """
        if not self.use_camera or self.camera is None:
            return False

        with self.camera_lock:
            try:
                # Pause streaming if active
                was_streaming = False
                if self.streaming_active and not self.streaming_paused:
                    self._pause_streaming_internal()
                    was_streaming = True

                # Set parameter using PyNikonSciCam API
                success = self._set_camera_parameter_internal(parameter, value)

                # Resume streaming if it was active
                if was_streaming:
                    self._resume_streaming_internal()

                return success

            except Exception as e:
                print(f"Failed to set camera parameter {parameter}: {e}")
                # Resume streaming on error if it was active
                if was_streaming and self.streaming_paused:
                    self._resume_streaming_internal()
                return False

    def _validate_parameter_value(self, parameter, value):
        """
        Validate parameter value before setting.

        Args:
            parameter: Parameter name
            value: Parameter value to validate

        Returns:
            tuple: (is_valid, validated_value, error_message)
        """
        try:
            # Define parameter ranges and validation rules
            validation_rules = {
                'exposure_time': {'type': int, 'min': 1, 'max': 10000},
                'gain': {'type': (int, float), 'min': 0.1, 'max': 10.0},
                'brightness': {'type': int, 'min': 0, 'max': 100},
                'sharpness': {'type': int, 'min': 0, 'max': 100},
                'hue': {'type': int, 'min': -180, 'max': 180},
                'saturation': {'type': int, 'min': 0, 'max': 100},
                'tone': {'type': int, 'min': 0, 'max': 100},
                'white_balance_red': {'type': int, 'min': 0, 'max': 255},
                'white_balance_blue': {'type': int, 'min': 0, 'max': 255},
                'white_balance_green': {'type': int, 'min': 0, 'max': 255},
                'exposure_mode': {'type': str, 'values': ['ContinuousAE', 'OnePushAE', 'Manual', 'MultiExposureTime']},
                'trigger_mode': {'type': str, 'values': ['Off', 'Hard', 'Soft']},
                'capture_mode': {'type': str, 'values': ['egcmNoGroup', 'egcmSoftHard', 'egcmSoftSoft']},
                'white_balance': {'type': str, 'values': ['wbManual', 'wbOnePush', 'wbAuto']},
            }

            if parameter not in validation_rules:
                return False, value, f"Unknown parameter: {parameter}"

            rule = validation_rules[parameter]

            # Type validation
            if not isinstance(value, rule['type']):
                try:
                    if rule['type'] == int:
                        value = int(value)
                    elif rule['type'] == float:
                        value = float(value)
                    elif rule['type'] == str:
                        value = str(value)
                    elif isinstance(rule['type'], tuple):
                        # Multiple allowed types
                        if int in rule['type']:
                            value = int(value)
                        elif float in rule['type']:
                            value = float(value)
                except (ValueError, TypeError):
                    return False, value, f"Invalid type for {parameter}: expected {rule['type']}, got {type(value)}"

            # Range validation
            if 'min' in rule and 'max' in rule:
                if value < rule['min'] or value > rule['max']:
                    # Clamp to valid range
                    value = max(rule['min'], min(rule['max'], value))
                    print(f"⚠ Parameter {parameter} clamped to valid range: {value}")

            # Value validation for enums
            if 'values' in rule:
                if value not in rule['values']:
                    return False, value, f"Invalid value for {parameter}: {value}. Valid values: {rule['values']}"

            return True, value, None

        except Exception as e:
            return False, value, f"Validation error for {parameter}: {e}"

    def _set_camera_parameter_internal(self, parameter, value):
        """
        Internal method to set camera parameter using PyNikonSciCam API.

        Args:
            parameter: Parameter name (string)
            value: Parameter value

        Returns:
            bool: True if parameter was set successfully
        """
        if not CAMERA_AVAILABLE or self.camera is None:
            return False

        try:
            # Validate parameter value first
            is_valid, validated_value, error_msg = self._validate_parameter_value(parameter, value)
            if not is_valid:
                print(f"✗ Parameter validation failed: {error_msg}")
                return False

            value = validated_value

            # Map parameter names to ECamFeatureId constants
            parameter_mapping = {
                'exposure_time': consts.ECamFeatureId.ExposureTime,
                'exposure_mode': consts.ECamFeatureId.ExposureMode,
                'gain': consts.ECamFeatureId.Gain,
                'brightness': consts.ECamFeatureId.Brightness,
                'sharpness': consts.ECamFeatureId.Sharpness,
                'hue': consts.ECamFeatureId.Hue,
                'white_balance_red': consts.ECamFeatureId.WhiteBalanceRed,
                'white_balance_blue': consts.ECamFeatureId.WhiteBalanceBlue,
                # 'capture_mode': consts.ECamFeatureId.CaptureMode,
            }

            if parameter not in parameter_mapping:
                print(f"Unknown parameter: {parameter}")
                return False

            feature_id = parameter_mapping[parameter]

            # Convert values for specific parameters
            if parameter == 'exposure_mode':
                # Convert string to enum value
                mode_mapping = {
                    'ContinuousAE': consts.ECamExposureMode.ContinuousAE,
                    'OnePushAE': consts.ECamExposureMode.OnePushAE,
                    'Manual': consts.ECamExposureMode.Manual,
                    'MultiExposureTime': consts.ECamExposureMode.MultiExposureTime
                }
                value = mode_mapping.get(value, consts.ECamExposureMode.ContinuousAE)
            # elif parameter == 'capture_mode':
            #     # Convert string to enum value
            #     capture_mapping = {
            #         'egcmNoGroup': consts.ECamGroupCaptureMode.egcmNoGroup,
            #         'egcmSoftHard': consts.ECamGroupCaptureMode.egcmSoftHard,
            #         'egcmSoftSoft': consts.ECamGroupCaptureMode.egcmSoftSoft
            #     }
            #     value = capture_mapping.get(value, consts.ECamGroupCaptureMode.egcmSoftSoft)

            # Set the feature value
            self.camera.set_feature_value(feature_id, value)

            # Round-trip verification: read back the value to confirm it was set
            try:
                actual_value = self.camera.get_feature_value(feature_id)

                # For enum parameters, compare the enum values
                if parameter in ['exposure_mode', 'capture_mode']:
                    if actual_value == value:
                        print(f"✓ Set camera parameter {parameter} to {value} (verified)")
                        return True
                    else:
                        print(f"⚠ Parameter {parameter} set to {value} but camera reports {actual_value}")
                        return True  # Still consider success as camera accepted some value
                else:
                    # For numeric parameters, allow small tolerance
                    tolerance = 1 if isinstance(value, int) else 0.1
                    if abs(actual_value - value) <= tolerance:
                        print(f"✓ Set camera parameter {parameter} to {value} (verified: {actual_value})")
                        return True
                    else:
                        print(f"⚠ Parameter {parameter} set to {value} but camera reports {actual_value}")
                        return True  # Still consider success as camera accepted some value

            except Exception as verify_error:
                print(f"⚠ Could not verify parameter {parameter} after setting: {verify_error}")
                print(f"✓ Set camera parameter {parameter} to {value} (verification failed)")
                return True  # Assume success if we can't verify

        except Exception as e:
            print(f"✗ Failed to set camera parameter {parameter}: {e}")
            return False

    def get_camera_parameters(self):
        """
        Get current camera parameters using PyNikonSciCam API.

        Returns:
            dict: Camera parameters or None if not available
        """
        if not self.use_camera or self.camera is None:
            return None

        try:
            # Get current parameter values from camera
            parameters = {}

            # Parameter mapping for reading values
            parameter_mapping = {
                'exposure_time': consts.ECamFeatureId.ExposureTime,
                'exposure_mode': consts.ECamFeatureId.ExposureMode,
                'gain': consts.ECamFeatureId.Gain,
                'brightness': consts.ECamFeatureId.Brightness,
                'sharpness': consts.ECamFeatureId.Sharpness,
                'hue': consts.ECamFeatureId.Hue,
                'saturation': consts.ECamFeatureId.Saturation,
                'white_balance_red': consts.ECamFeatureId.WhiteBalanceRed,
                'white_balance_blue': consts.ECamFeatureId.WhiteBalanceBlue,
                # 'capture_mode': consts.ECamFeatureId.CaptureMode,
            }

            for param_name, feature_id in parameter_mapping.items():
                try:
                    value = self.camera.get_feature_value(feature_id)

                    # Convert enum values back to strings for UI
                    if param_name == 'exposure_mode':
                        mode_names = {
                            consts.ECamExposureMode.ContinuousAE: 'ContinuousAE',
                            consts.ECamExposureMode.OnePushAE: 'OnePushAE',
                            consts.ECamExposureMode.Manual: 'Manual',
                            consts.ECamExposureMode.MultiExposureTime: 'MultiExposureTime'
                        }
                        value = mode_names.get(value, 'ContinuousAE')
                    # elif param_name == 'capture_mode':
                    #     capture_names = {
                    #         consts.ECamGroupCaptureMode.egcmNoGroup: 'egcmNoGroup',
                    #         consts.ECamGroupCaptureMode.egcmSoftHard: 'egcmSoftHard',
                    #         consts.ECamGroupCaptureMode.egcmSoftSoft: 'egcmSoftSoft'
                    #     }
                    #     value = capture_names.get(value, 'egcmSoftSoft')

                    parameters[param_name] = value

                except Exception as e:
                    print(f"⚠ Could not read parameter {param_name}: {e}")
                    # Set default values for parameters that can't be read
                    default_values = {
                        'exposure_time': 100,
                        'exposure_mode': 'ContinuousAE',
                        'gain': 1.0,
                        'brightness': 50,
                        'sharpness': 50,
                        'hue': 0,
                        'saturation': 50,
                        'tone': 50,
                        'white_balance_red': 64,
                        'white_balance_blue': 64,
                        'capture_mode': 'egcmSoftSoft'
                    }
                    parameters[param_name] = default_values.get(param_name, 0)

            return parameters

        except Exception as e:
            print(f"✗ Failed to get camera parameters: {e}")
            return None

    def disconnect(self):
        """Disconnect camera."""
        with self.camera_lock:
            if self.streaming_active:
                self.stop_streaming()
            if self.camera is not None:
                try:
                    self.camera.disconnect()
                except:
                    pass
                self.camera = None

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()


class CameraManager:
    """
    Legacy CameraManager wrapper that uses the centralized camera manager.
    Maintains backward compatibility while using the shared camera instance.
    """

    def __init__(self, camera_index=0, region=None, test_camera=True):
        """
        Initialize camera manager using centralized access.

        Args:
            camera_index: Camera index (0 for real camera, 3 for simulator)
            region: Region for screenshot fallback (deprecated)
            test_camera: Whether to test camera connection on init
        """
        self.camera_index = camera_index
        self.region = region
        self.centralized_manager = CentralizedCameraManager(camera_index)
        self.use_camera = self.centralized_manager.use_camera

        if test_camera and self.use_camera:
            try:
                # Test camera access through centralized manager
                img = self.centralized_manager.get_image()
                if img is not None:
                    print(f"✓ Camera test successful with ID {self.camera_index}")
                else:
                    raise RuntimeError("Camera test returned None image")
            except Exception as e:
                print(f"⚠ Camera test failed: {e}")
                self.use_camera = False

    def get_image(self):
        """
        Capture image using centralized camera manager.

        Returns:
            numpy.ndarray: Captured image

        Raises:
            RuntimeError: If camera is not available or capture fails
        """
        return self.centralized_manager.get_image()

    def disconnect(self):
        """Disconnect camera (managed by centralized manager)."""
        pass  # Centralized manager handles disconnection

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()


class NikonCameraExtended(NikonCamera):
    """
    Extended NikonCamera class with live streaming capabilities.
    Designed to work with the centralized camera manager for shared access.
    """

    def __init__(self, camera_index):
        """Initialize the extended camera with connection."""
        self.connected = False
        self._frame_transfer_active = False
        super().__init__(camera_index)
        self.connect()

    def connect(self):
        """Connect to the camera."""
        if not self.connected:
            super().connect()
            self.connected = True

    def disconnect(self):
        """Disconnect from the camera."""
        if self.connected:
            try:
                if hasattr(self, '_frame_transfer_active') and self._frame_transfer_active:
                    self.stop_live()
            except:
                pass
            super().disconnect()
            self.connected = False

    def start_live(self):
        """Start live streaming by setting free-run mode and beginning frame transfer."""
        if not CAMERA_AVAILABLE or not self.connected:
            raise RuntimeError("Camera not available or not connected")

        try:
            self.set_trigger_mode(consts.ECamTriggerMode.Off)
            self._start_FrameTransfer()
            self._frame_transfer_active = True
        except Exception as e:
            raise RuntimeError(f"Failed to start live streaming: {e}")

    def stop_live(self):
        """Stop live streaming by ending frame transfer."""
        if not CAMERA_AVAILABLE or not self.connected:
            return

        try:
            if hasattr(self, '_frame_transfer_active') and self._frame_transfer_active:
                self._stop_FrameTransfer()
                self._frame_transfer_active = False
        except Exception as e:
            print(f"Warning: Failed to stop live streaming: {e}")

    def get_live_image(self):
        """Get the latest image during live streaming without stopping transfer."""
        if not CAMERA_AVAILABLE or not self.connected:
            return None

        if not hasattr(self, '_frame_transfer_active') or not self._frame_transfer_active:
            return None

        if self._stImage is None:
            return None

        try:
            event_or_none = methods.poll_event(self.camera_handle, consts.ECamEventType.ecetImageReceived)
            if event_or_none is not None and event_or_none.eEventType == consts.ECamEventType.ecetImageReceived:
                methods.get_image(self.camera_handle, self._stImage, b_newest_required=True)
                height, width = self.height, self.width
                expected_size = height * width * 3
                img = np.ctypeslib.as_array(self._stImage.pDataBuffer, shape=(self._stImage.uiDataBufferSize,))
                if img.size < expected_size:
                    return None
                img = img[:expected_size].reshape((height, width, 3))[..., ::-1]  # Convert to RGB
                return img.astype(np.uint8)
        except Exception:
            return None
        return None


class CameraWorker(QThread):
    """
    Worker thread to capture images continuously from the centralized camera manager.
    Uses the shared camera instance to avoid concurrent access conflicts.
    """
    imageCaptured = pyqtSignal(np.ndarray)
    errorOccurred = pyqtSignal(str)

    def __init__(self, camera_index=0):
        super().__init__()
        self.camera_index = camera_index
        self.stopped = False
        self.centralized_manager = CentralizedCameraManager(camera_index)

    def run(self):
        """Start live streaming and capture images using centralized manager."""
        if not CAMERA_AVAILABLE:
            self.errorOccurred.emit("PyNikonSciCam not available")
            return

        try:
            # Start streaming through centralized manager
            if not self.centralized_manager.start_streaming():
                self.errorOccurred.emit("Failed to start camera streaming")
                return

            while not self.stopped:
                img = self.centralized_manager.get_streaming_image()
                if img is not None:
                    self.imageCaptured.emit(img)
                else:
                    time.sleep(0.01)  # Brief sleep if no image is available

        except Exception as e:
            self.errorOccurred.emit(f"Error in camera worker thread: {str(e)}")
        finally:
            # Stop streaming when thread ends
            try:
                self.centralized_manager.stop_streaming()
            except:
                pass

    def stop(self):
        """Signal the thread to stop."""
        self.stopped = True


class VideoStreamingManager:
    """
    High-level manager for video streaming operations using centralized camera access.
    Provides a clean interface for GUI components while coordinating with scanning operations.
    """

    def __init__(self, camera_index=0):
        """
        Initialize video streaming manager.

        Args:
            camera_index: Camera index (0 for real camera, 3 for simulator)
        """
        self.camera_index = camera_index
        self.worker = None
        self.is_streaming = False
        self.centralized_manager = CentralizedCameraManager(camera_index)

    def start_streaming(self, image_callback=None, error_callback=None):
        """
        Start video streaming using centralized camera manager.

        Args:
            image_callback: Function to call when new image is captured
            error_callback: Function to call when error occurs

        Returns:
            bool: True if streaming started successfully
        """
        if self.is_streaming:
            return True

        if not CAMERA_AVAILABLE:
            if error_callback:
                error_callback("PyNikonSciCam not available")
            return False

        try:
            self.worker = CameraWorker(self.camera_index)

            if image_callback:
                self.worker.imageCaptured.connect(image_callback)
            if error_callback:
                self.worker.errorOccurred.connect(error_callback)

            self.worker.start()
            self.is_streaming = True
            return True

        except Exception as e:
            if error_callback:
                error_callback(f"Failed to start streaming: {str(e)}")
            return False

    def stop_streaming(self):
        """Stop video streaming."""
        if not self.is_streaming or not self.worker:
            return

        self.worker.stop()
        self.worker.wait()
        self.worker = None
        self.is_streaming = False

    def is_streaming_active(self):
        """Check if streaming is currently active."""
        return self.is_streaming and self.worker is not None

    def get_camera_parameters(self):
        """
        Get current camera parameters through centralized manager.

        Returns:
            dict: Camera parameters or None if not available
        """
        if not CAMERA_AVAILABLE:
            return None

        # Use centralized manager to get parameters with proper coordination
        return self.centralized_manager.get_camera_parameters()

    def set_camera_parameter(self, parameter, value):
        """
        Set camera parameter through centralized manager.

        Args:
            parameter: Parameter name
            value: Parameter value

        Returns:
            bool: True if parameter was set successfully
        """
        if not CAMERA_AVAILABLE:
            return False

        # Use centralized manager to set parameter with proper coordination
        return self.centralized_manager.set_camera_parameter(parameter, value)
