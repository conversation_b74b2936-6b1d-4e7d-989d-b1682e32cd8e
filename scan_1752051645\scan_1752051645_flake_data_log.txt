================================================================================
SCANNING OPERATION LOG - 2025-07-09 17:00:46
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\scan_1752051645\scan_1752051645_flake_data.csv
Grid Steps: 0 x 0
Edge Method: general
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\scan_1752051645
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\scan_1752051645\debug_screenshots
================================================================================

[2025-07-09 17:00:46.692] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-09 17:00:46.702] [INFO] [SYSTEM] Using custom scan folder: scan_1752051645
[2025-07-09 17:00:46.721] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-09 17:00:46.839] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-09 17:00:46.849] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-09 17:00:46.859] [INFO] [STATUS] ✗ SCAN ABORTED: Unexpected error in corner detection: 'NoneType' object has no attribute 'shape'
[2025-07-09 17:00:46.880] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\scan_1752051645\scan_1752051645_flake_data.csv
[2025-07-09 17:00:46.889] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-09 17:00:46.899] [INFO] [STATUS] Scanning for annotated images...
[2025-07-09 17:00:46.909] [WARNING] [WARNING] ⚠ Warning: Could not create composite image: No annotated images found
