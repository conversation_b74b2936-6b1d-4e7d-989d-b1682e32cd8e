================================================================================
SCANNING OPERATION LOG - 2025-07-10 10:33:54
================================================================================
Mode: adaptive
Output CSV: Z:\A.Members\张恩浩\python\transfer\20250710\20250710_flake_data.csv
Grid Steps: 0 x 0
Edge Method: background
Hybrid Corner Alignment: True
Reference File: None
Scan Folder: Z:\A.Members\张恩浩\python\transfer\20250710
Debug Screenshots: Z:\A.Members\张恩浩\python\transfer\20250710\debug_screenshots
================================================================================

[2025-07-10 10:33:54.208] [INFO] [SYSTEM] Logging system initialized successfully
[2025-07-10 10:33:54.217] [INFO] [SYSTEM] Using custom scan folder: 20250710
[2025-07-10 10:33:54.233] [INFO] [SUCCESS] ✓ Camera streaming already active
[2025-07-10 10:33:54.285] [INFO] [CALIBRATION] Setting initial zero reference point for adaptive scan...
[2025-07-10 10:33:54.423] [INFO] [SUCCESS] ✓ Initial zero reference point set successfully
[2025-07-10 10:33:54.487] [INFO] [WORKFLOW] Finding chip upper-left corner...
[2025-07-10 10:33:55.382] [INFO] [WORKFLOW] Finding left edge (rotation-robust)...
[2025-07-10 10:33:57.700] [INFO] [POSITION] Position feedback: (4.02, 0.00) μm
[2025-07-10 10:33:59.541] [INFO] [POSITION] Position feedback: (199.78, 0.00) μm
[2025-07-10 10:34:01.351] [INFO] [POSITION] Position feedback: (355.32, 0.00) μm
[2025-07-10 10:34:03.180] [INFO] [POSITION] Position feedback: (527.80, 0.00) μm
[2025-07-10 10:34:04.836] [INFO] [STATUS] Found left edge (rotation-robust)
[2025-07-10 10:34:04.980] [INFO] [POSITION] Position feedback: (691.17, 0.00) μm
[2025-07-10 10:34:05.197] [INFO] [WORKFLOW] Finding top edge (rotation-robust)...
[2025-07-10 10:34:06.397] [INFO] [STATUS] Found top edge (rotation-robust)
[2025-07-10 10:34:06.560] [INFO] [POSITION] Position feedback: (691.17, 0.00) μm
[2025-07-10 10:34:07.071] [INFO] [WORKFLOW] Starting position (rotation-robust): (0.0, 691.2) μm
[2025-07-10 10:34:07.081] [INFO] [CALIBRATION] Setting zero reference point at upper-left corner...
[2025-07-10 10:34:07.196] [INFO] [SUCCESS] ✓ Zero reference point set successfully
[2025-07-10 10:34:07.206] [INFO] [STATUS] Creating automatic hybrid corner reference...
[2025-07-10 10:34:07.215] [INFO] [STATUS] Creating hybrid corner reference from current position...
[2025-07-10 10:34:07.335] [INFO] [SUCCESS] ✓ Corner reference image saved: Z:\A.Members\张恩浩\python\transfer\20250710\20250710_corner_image.png
[2025-07-10 10:34:07.347] [INFO] [STATUS] Detecting flakes in corner region...
[2025-07-10 10:34:23.938] [INFO] [STATUS] Found 0 flakes in corner region
[2025-07-10 10:34:23.950] [INFO] [STATUS] [10:34:23.950] Creating hybrid corner reference...
[2025-07-10 10:34:24.029] [INFO] [STATUS] [10:34:24.029] Current stage position: (0.00, 0.00) μm
[2025-07-10 10:34:24.040] [INFO] [STATUS] [10:34:24.040] Detecting hybrid features (flakes + edge keypoints)...
[2025-07-10 10:34:24.678] [INFO] [STATUS] [10:34:24.678] Detected 161 total features:
[2025-07-10 10:34:24.702] [INFO] [STATUS] [10:34:24.701]   - 0 flakes
[2025-07-10 10:34:24.711] [INFO] [STATUS] [10:34:24.711]   - 161 edge keypoints
[2025-07-10 10:34:24.722] [INFO] [STATUS] [10:34:24.722] Generating geometric hash table...
[2025-07-10 10:34:24.868] [INFO] [STATUS] [10:34:24.868] Hash table generation completed in 0.14 seconds
[2025-07-10 10:34:24.889] [INFO] [STATUS] [10:34:24.889] Generated 2946 hash buckets
[2025-07-10 10:34:24.900] [INFO] [STATUS] [10:34:24.900] Total triplets stored: 5000
[2025-07-10 10:34:24.925] [INFO] [STATUS] [10:34:24.925] Hybrid corner reference created successfully with 161 features
[2025-07-10 10:34:24.936] [INFO] [SUCCESS] ✓ Automatic hybrid corner reference created successfully
[2025-07-10 10:34:24.946] [INFO] [STATUS] DEBUG: About to save reference to: Z:\A.Members\张恩浩\python\transfer\20250710\20250710_hybrid_reference.json
[2025-07-10 10:34:24.956] [INFO] [STATUS] DEBUG: Reference data keys: ['format_version', 'alignment_method', 'creation_timestamp', 'corner_origin_abs', 'features', 'hash_table', 'feature_counts', 'detection_methods', 'metadata', 'edge_detection_method', 'creation_mode', 'scan_mode', 'corner_image_file']
[2025-07-10 10:34:26.056] [INFO] [STATUS] DEBUG: save_hybrid_reference returned: True
[2025-07-10 10:34:26.065] [INFO] [SUCCESS] ✓ Reference saved to: Z:\A.Members\张恩浩\python\transfer\20250710\20250710_hybrid_reference.json
[2025-07-10 10:34:26.138] [INFO] [SUCCESS] ✓ MANDATORY hybrid corner reference creation completed successfully
[2025-07-10 10:34:26.150] [INFO] [STATUS] DEBUG: About to return True from _create_automatic_hybrid_corner_reference
[2025-07-10 10:34:26.175] [INFO] [WORKFLOW] Starting adaptive scan...
[2025-07-10 10:34:26.188] [INFO] [WORKFLOW] 
=== Starting Row 0 ===
[2025-07-10 10:34:26.197] [INFO] [STATUS] Scanning row 0 rightward...
[2025-07-10 10:34:45.094] [INFO] [PROGRESS] Progress: 1/11 (9.1%)
[2025-07-10 10:34:45.105] [INFO] [STATUS] Position (0,0): Found 0 flakes
[2025-07-10 10:34:45.242] [INFO] [POSITION] Position feedback: (-4.02, 0.00) μm
[2025-07-10 10:35:07.639] [INFO] [PROGRESS] Progress: 2/12 (16.7%)
[2025-07-10 10:35:07.651] [INFO] [STATUS] Position (0,1): Found 0 flakes
[2025-07-10 10:35:07.787] [INFO] [POSITION] Position feedback: (-349.82, 0.00) μm
[2025-07-10 10:35:27.901] [INFO] [PROGRESS] Progress: 3/13 (23.1%)
[2025-07-10 10:35:27.911] [INFO] [STATUS] Position (0,2): Found 0 flakes
[2025-07-10 10:35:28.037] [INFO] [POSITION] Position feedback: (-695.19, 0.00) μm
[2025-07-10 10:35:48.527] [INFO] [PROGRESS] Progress: 4/14 (28.6%)
[2025-07-10 10:35:48.536] [INFO] [STATUS] Position (0,3): Found 0 flakes
[2025-07-10 10:35:48.656] [INFO] [POSITION] Position feedback: (-1040.99, 0.00) μm
[2025-07-10 10:36:08.262] [INFO] [PROGRESS] Progress: 5/15 (33.3%)
[2025-07-10 10:36:08.286] [INFO] [STATUS] Position (0,4): Found 1 flakes
[2025-07-10 10:36:08.416] [INFO] [POSITION] Position feedback: (-1391.87, 0.00) μm
[2025-07-10 10:36:28.555] [INFO] [PROGRESS] Progress: 6/16 (37.5%)
[2025-07-10 10:36:28.581] [INFO] [STATUS] Position (0,5): Found 0 flakes
[2025-07-10 10:36:28.714] [INFO] [POSITION] Position feedback: (-1731.95, 0.00) μm
[2025-07-10 10:36:49.281] [INFO] [PROGRESS] Progress: 7/17 (41.2%)
[2025-07-10 10:36:49.291] [INFO] [STATUS] Position (0,6): Found 0 flakes
[2025-07-10 10:36:49.437] [INFO] [POSITION] Position feedback: (-2077.75, 0.00) μm
[2025-07-10 10:37:10.178] [INFO] [PROGRESS] Progress: 8/18 (44.4%)
[2025-07-10 10:37:10.202] [INFO] [STATUS] Position (0,7): Found 0 flakes
[2025-07-10 10:37:10.324] [INFO] [POSITION] Position feedback: (-2423.33, 0.00) μm
[2025-07-10 10:38:01.133] [INFO] [PROGRESS] Progress: 9/19 (47.4%)
[2025-07-10 10:38:01.150] [INFO] [STATUS] Position (0,8): Found 1 flakes
[2025-07-10 10:38:01.282] [INFO] [POSITION] Position feedback: (-2768.92, 0.00) μm
[2025-07-10 10:38:21.150] [INFO] [PROGRESS] Progress: 10/20 (50.0%)
[2025-07-10 10:38:21.203] [INFO] [STATUS] Position (0,9): Found 0 flakes
[2025-07-10 10:38:21.338] [INFO] [POSITION] Position feedback: (-3114.51, 0.00) μm
[2025-07-10 10:38:24.359] [INFO] [STATUS] Right edge detected: 1/5 right-side points on chip
[2025-07-10 10:38:24.370] [INFO] [STATUS] Row 0: Reached right edge at column 10
[2025-07-10 10:38:24.380] [INFO] [STATUS] Row 0 complete: 10 positions scanned
[2025-07-10 10:38:24.390] [INFO] [WORKFLOW] 
=== Starting Row 1 ===
[2025-07-10 10:38:24.531] [INFO] [POSITION] Position feedback: (-3451.84, -4.02) μm
[2025-07-10 10:38:29.546] [INFO] [STATUS] Reached bottom edge of chip at row 1
[2025-07-10 10:38:29.565] [INFO] [STATUS] 
=== Scan Complete ===
[2025-07-10 10:38:29.575] [INFO] [STATUS] Total positions: 10
[2025-07-10 10:38:29.585] [INFO] [STATUS] Total rows: 1
[2025-07-10 10:38:29.610] [INFO] [WORKFLOW] ADAPTIVE SCANNING workflow completed - SUCCESS - Output: Z:\A.Members\张恩浩\python\transfer\20250710\20250710_flake_data.csv
[2025-07-10 10:38:29.621] [INFO] [STATUS] 
=== CREATING COMPOSITE ANNOTATED IMAGE ===
[2025-07-10 10:38:29.630] [INFO] [STATUS] Scanning for annotated images...
[2025-07-10 10:38:29.642] [INFO] [STATUS] Found 10 annotated images
[2025-07-10 10:38:29.662] [INFO] [STATUS] Processing 2 valid step positions
[2025-07-10 10:38:29.674] [INFO] [STATUS] Grid dimensions: 5 x 1
[2025-07-10 10:38:29.832] [INFO] [STATUS] Creating composite image: 14400 x 2048 pixels
[2025-07-10 10:38:30.104] [ERROR] [ERROR] ⚠ Warning: Could not create composite image: Failed to save composite image
