#!/usr/bin/env python3
"""
Enhanced Edge-detecting Scanner with Auto-Alignment - Main Entry Point

This is the main entry point for the modular edge-detecting scanner system.
It coordinates all modules and provides both GUI and command-line interfaces.

Features:
- Modular architecture with separate modules for different functionality
- Enhanced rotation robustness for chips rotated >20 degrees
- Improved edge detection algorithms
- Robust shape matching and alignment
- User-friendly GUI with comprehensive testing capabilities

Usage:
    python scanner.py                    # Launch GUI
    python scanner.py --help            # Show command line options
    python scanner.py --test-edge       # Test edge detection only
    python scanner.py --test-align      # Test alignment only

Author: Enhanced by AI Assistant
Version: 2.0
"""

import sys
import argparse
import traceback
from pathlib import Path

# Import all modules
try:
    from config import *
    from edge_detection import EdgeDetector, BackgroundEdgeDetector, NoEdgeDetector, CannyEdgeDetector, Hough<PERSON>ineFitter, RANSACLineFitter
    from scanning import Flake  # Flake class moved to scanning.py
    # ShapeMatcher and CoordinateTransformer are no longer needed
    from scanning import <PERSON><PERSON><PERSON>roll<PERSON>, <PERSON>an<PERSON>orker
    from ui_components import <PERSON>App, FlakeSelector, create_application
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Make sure all required dependencies are installed:")
    print("- PyQt6")
    print("- opencv-python")
    print("- numpy")
    print("- supervision")
    print("- inference-sdk")
    print("- PyNikonSciCam")
    sys.exit(1)


def main():
    """Main entry point with command line argument parsing"""
    parser = argparse.ArgumentParser(
        description="Enhanced Edge-detecting Scanner with Auto-Alignment",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python scanner.py                    # Launch GUI
  python scanner.py --test-edge       # Test edge detection
  python scanner.py --test-align      # Test alignment
  python scanner.py --version         # Show version info

For full functionality, use the GUI mode (no arguments).
        """
    )

    parser.add_argument('--test-edge', action='store_true',
                       help='Test edge detection algorithms')
    parser.add_argument('--test-align', action='store_true',
                       help='Test alignment algorithms')
    parser.add_argument('--version', action='store_true',
                       help='Show version information')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug mode')

    args = parser.parse_args()

    if args.version:
        print("Enhanced Edge-detecting Scanner with Auto-Alignment")
        print("Version: 2.0")
        print("Features: Modular architecture, rotation robustness, enhanced alignment")
        return

    # Default: Launch GUI
    print("Launching Enhanced Edge-detecting Scanner GUI...")
    print("Features:")
    print("- Modular architecture for better maintainability")
    print("- Enhanced rotation robustness (>20 degree rotation support)")
    print("- Improved edge detection algorithms")
    print("- Robust shape matching and alignment")
    print("- Comprehensive testing capabilities")
    print()

    try:
        app, main_window = create_application()

        if args.debug:
            print("Debug mode enabled")
            # Could add debug-specific configurations here

        print("GUI launched successfully. Close the window to exit.")
        sys.exit(app.exec())

    except Exception as e:
        print(f"Failed to launch GUI: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()