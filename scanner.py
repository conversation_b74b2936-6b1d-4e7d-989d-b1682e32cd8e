#!/usr/bin/env python3
"""
Enhanced Edge-detecting Scanner with Auto-Alignment - Main Entry Point

This is the main entry point for the modular edge-detecting scanner system.
It coordinates all modules and provides both GUI and command-line interfaces.

Features:
- Modular architecture with separate modules for different functionality
- Enhanced rotation robustness for chips rotated >20 degrees
- Improved edge detection algorithms
- Robust shape matching and alignment
- User-friendly GUI with comprehensive testing capabilities

Usage:
    python scanner.py                    # Launch GUI
    python scanner.py --help            # Show command line options
    python scanner.py --test-edge       # Test edge detection only
    python scanner.py --test-align      # Test alignment only

Author: Enhanced by AI Assistant
Version: 2.0
"""

import sys
import argparse
import traceback
from pathlib import Path

# Import all modules
try:
    from config import *
    from edge_detection import EdgeDetector, BackgroundEdgeDetector, NoEdgeDetector, CannyEdgeDetector, Hough<PERSON>ineFitter, RANSACLineFitter
    from scanning import Flake  # Flake class moved to scanning.py
    # ShapeMatcher and CoordinateTransformer are no longer needed
    from scanning import <PERSON><PERSON><PERSON>roll<PERSON>, <PERSON>an<PERSON>orker
    from ui_components import <PERSON>App, FlakeSelector, create_application
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Make sure all required dependencies are installed:")
    print("- PyQt6")
    print("- opencv-python")
    print("- numpy")
    print("- supervision")
    print("- inference-sdk")
    print("- PyNikonSciCam")
    sys.exit(1)


def test_edge_detection():
    """Test edge detection functionality from command line"""
    print("Testing edge detection...")

    try:
        import cv2
        import numpy as np
        from camera_manager import CentralizedCameraManager

        # Capture current camera view
        camera_manager = CentralizedCameraManager(camera_index=0)
        img = camera_manager.get_streaming_image()
        # Camera disconnection handled by CentralizedCameraManager

        methods = [
            ('General', EdgeDetector(debug=False)),
            ('Background', BackgroundEdgeDetector(debug=False)),
            ('Canny', CannyEdgeDetector(debug=False))
        ]

        print(f"Testing on image of size: {img.shape}")

        for method_name, detector in methods:
            print(f"\n--- Testing {method_name} Method ---")

            mask = detector.detect_chip_edges(img)
            h, w = mask.shape
            center_x, center_y = w // 2, h // 2

            # Test key points
            test_points = [
                (center_x, center_y, "Center"),
                (int(w * 0.2), center_y, "Left"),
                (int(w * 0.8), center_y, "Right"),
                (center_x, int(h * 0.2), "Top"),
                (center_x, int(h * 0.8), "Bottom")
            ]

            on_chip_count = 0
            for x, y, label in test_points:
                on_chip = detector.is_on_chip(img, x, y)
                print(f"  {label}: {'ON chip' if on_chip else 'OFF chip'}")
                if on_chip:
                    on_chip_count += 1

            coverage = on_chip_count / len(test_points) * 100
            print(f"  Coverage: {coverage:.1f}%")

            # Save results
            cv2.imwrite(f"test_{method_name.lower()}_mask.png", mask)
            print(f"  Saved: test_{method_name.lower()}_mask.png")

        print("\nEdge detection test completed!")

    except Exception as e:
        print(f"Edge detection test failed: {e}")
        traceback.print_exc()


def test_alignment():
    """Test alignment functionality from command line"""
    print("Testing alignment...")

    try:
        # This would require two CSV files to test
        print("Alignment testing requires two scan CSV files.")
        print("Please use the GUI version (python scanner.py) for interactive alignment testing.")

    except Exception as e:
        print(f"Alignment test failed: {e}")
        traceback.print_exc()


def main():
    """Main entry point with command line argument parsing"""
    parser = argparse.ArgumentParser(
        description="Enhanced Edge-detecting Scanner with Auto-Alignment",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python scanner.py                    # Launch GUI
  python scanner.py --test-edge       # Test edge detection
  python scanner.py --test-align      # Test alignment
  python scanner.py --version         # Show version info

For full functionality, use the GUI mode (no arguments).
        """
    )

    parser.add_argument('--test-edge', action='store_true',
                       help='Test edge detection algorithms')
    parser.add_argument('--test-align', action='store_true',
                       help='Test alignment algorithms')
    parser.add_argument('--version', action='store_true',
                       help='Show version information')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug mode')

    args = parser.parse_args()

    if args.version:
        print("Enhanced Edge-detecting Scanner with Auto-Alignment")
        print("Version: 2.0")
        print("Features: Modular architecture, rotation robustness, enhanced alignment")
        return

    if args.test_edge:
        test_edge_detection()
        return

    if args.test_align:
        test_alignment()
        return

    # Default: Launch GUI
    print("Launching Enhanced Edge-detecting Scanner GUI...")
    print("Features:")
    print("- Modular architecture for better maintainability")
    print("- Enhanced rotation robustness (>20 degree rotation support)")
    print("- Improved edge detection algorithms")
    print("- Robust shape matching and alignment")
    print("- Comprehensive testing capabilities")
    print()

    try:
        app, main_window = create_application()

        if args.debug:
            print("Debug mode enabled")
            # Could add debug-specific configurations here

        print("GUI launched successfully. Close the window to exit.")
        sys.exit(app.exec())

    except Exception as e:
        print(f"Failed to launch GUI: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()